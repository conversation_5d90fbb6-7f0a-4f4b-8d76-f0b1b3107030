2025-05-20 20:45:24,525 INFO /usr/local/bin/bench start
2025-05-20 20:45:25,288 INFO /usr/local/bin/bench watch
2025-05-20 20:45:25,330 INFO /usr/local/bin/bench schedule
2025-05-20 20:45:25,346 INFO /usr/local/bin/bench serve --port 8000
2025-05-20 20:45:25,378 INFO /usr/local/bin/bench worker
2025-05-20 21:11:44,782 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 21:12:01,009 INFO /usr/local/bin/bench --site site1.local list-apps
2025-05-20 21:12:19,628 INFO /usr/local/bin/bench --site site1.local migrate
2025-05-20 21:14:30,591 INFO /usr/local/bin/bench --site site1.local migrate
2025-05-20 21:15:38,020 INFO /usr/local/bin/bench --site site1.local uninstall-app whatsapp_integration
2025-05-20 21:16:34,267 INFO /usr/local/bin/bench --site site1.local list-apps
2025-05-20 21:18:00,396 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 21:20:24,461 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 21:20:53,682 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 21:21:06,920 INFO /usr/local/bin/bench --site site1.local list-apps
2025-05-20 21:21:18,720 INFO /usr/local/bin/bench restart
2025-05-20 21:21:37,433 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-20 21:21:38,591 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-20 21:21:38,602 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-20 21:21:40,633 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-20 21:21:50,078 INFO /usr/local/bin/bench restart
2025-05-20 21:21:52,841 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-20 21:21:54,146 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-20 21:21:54,146 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-20 21:21:55,620 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-20 21:22:08,793 INFO /usr/local/bin/bench start
2025-05-20 21:22:13,505 INFO /usr/local/bin/bench --site site1.local list-apps
2025-05-20 21:22:25,035 INFO /usr/local/bin/bench --site site1.local console
2025-05-20 21:24:11,960 INFO /usr/local/bin/bench make-app whatsapp_integration
2025-05-20 21:24:38,053 INFO /usr/local/bin/bench --site site1.local make-doctype WhatsApp Account --module WhatsApp Integration
2025-05-20 21:24:47,499 INFO /usr/local/bin/bench --site site1.local add-to-hosts
2025-05-20 21:36:56,278 INFO /usr/local/bin/bench --site site1.local migrate
2025-05-20 21:38:02,895 INFO /usr/local/bin/bench restart
2025-05-20 21:38:30,143 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-20 21:38:32,225 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-20 21:38:32,226 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-20 21:38:33,578 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-20 21:40:20,193 INFO /usr/local/bin/bench --site site1.local list-apps
2025-05-20 21:40:42,560 INFO /usr/local/bin/bench --site site1.local console
2025-05-20 21:42:42,922 INFO /usr/local/bin/bench --site site1.local --force reinstall
2025-05-20 21:48:12,561 INFO /usr/local/bin/bench --site site1.local build
2025-05-20 21:50:12,606 INFO /usr/local/bin/bench --site site1.local migrate
2025-05-20 21:50:39,190 INFO /usr/local/bin/bench restart
2025-05-20 21:50:51,882 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-20 21:50:52,491 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-20 21:50:52,495 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-20 21:50:54,528 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-20 21:51:06,916 INFO /usr/local/bin/bench start
2025-05-20 21:51:34,825 INFO /usr/local/bin/bench stop
2025-05-20 21:51:44,486 INFO /usr/local/bin/bench setup supervisor
2025-05-20 21:51:45,112 LOG Updated supervisord.conf: 'chmod' changed from '0700                       ; sockef file mode (default 0700)' to '0760'
2025-05-20 21:51:45,112 LOG Updated supervisord.conf: 'chown' changed from '' to 'newsmart:newsmart'
2025-05-20 21:51:46,418 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-20 21:51:54,534 INFO /usr/local/bin/bench restart
2025-05-20 21:51:55,560 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-20 21:51:56,212 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-20 21:51:56,213 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-20 21:51:57,049 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-20 21:52:07,014 INFO /usr/local/bin/bench start
2025-05-20 21:54:09,562 INFO /usr/local/bin/bench --site site1.local console
2025-05-20 21:58:58,532 INFO /usr/local/bin/bench restart
2025-05-20 21:58:59,414 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-20 21:58:59,963 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-20 21:58:59,964 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-20 21:59:01,005 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-20 22:00:03,241 INFO /usr/local/bin/bench start
2025-05-20 22:01:12,389 INFO /usr/local/bin/bench start
2025-05-20 22:01:28,259 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 22:01:40,111 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-20 22:01:51,626 INFO /usr/local/bin/bench start
2025-05-20 22:03:04,664 INFO /usr/local/bin/bench --site site1.local mariadb
2025-05-20 22:04:09,489 INFO /usr/local/bin/bench --site site1.local rebuild-global-search
2025-05-20 22:04:20,755 INFO /usr/local/bin/bench --site site1.local migrate
2025-05-20 22:04:33,572 INFO /usr/local/bin/bench --site site1.local reinstall
2025-05-20 22:05:58,792 INFO /usr/local/bin/bench start
2025-05-20 22:06:15,993 INFO /usr/local/bin/bench --site site1.local mariadb
2025-05-20 22:06:33,213 INFO /usr/local/bin/bench --site site1.local mariadb
2025-05-20 22:06:48,903 INFO /usr/local/bin/bench --site site1.local set-config maintenance_mode 1
2025-05-20 22:06:59,884 INFO /usr/local/bin/bench --site site1.local --force reinstall
2025-05-20 22:07:24,489 INFO /usr/local/bin/bench --site site1.local set-config maintenance_mode 0
2025-05-20 22:07:34,851 INFO /usr/local/bin/bench start
2025-05-20 22:08:44,199 INFO /usr/local/bin/bench --site site1.local --force reinstall
2025-05-20 22:11:52,323 INFO /usr/local/bin/bench start
2025-05-20 22:11:52,708 INFO /usr/local/bin/bench watch
2025-05-20 22:11:52,748 INFO /usr/local/bin/bench worker
2025-05-20 22:11:52,839 INFO /usr/local/bin/bench serve --port 8000
2025-05-20 22:11:52,955 INFO /usr/local/bin/bench schedule
2025-05-20 22:13:34,739 INFO /usr/local/bin/bench --site site1.local list-apps
2025-05-20 22:15:28,103 INFO /usr/local/bin/bench --site site1.local install-app erpnext
2025-05-20 22:16:40,353 INFO /usr/local/bin/bench --site site1.local reinstall-app erpnext
2025-05-20 22:16:55,148 INFO /usr/local/bin/bench --site site1.local migrate
2025-05-20 22:17:22,033 INFO /usr/local/bin/bench --site site1.local install-app erpnext
2025-05-20 22:18:07,990 INFO /usr/local/bin/bench --site site1.local backup
2025-05-20 22:18:29,809 INFO /usr/local/bin/bench --site site1.local backup
2025-05-20 22:18:44,687 INFO /usr/local/bin/bench --site site1.local --force reinstall
2025-05-20 22:20:03,050 INFO /usr/local/bin/bench --site site1.local backup
2025-05-20 22:20:18,195 INFO /usr/local/bin/bench --site site1.local --force reinstall
2025-05-20 22:23:31,128 INFO /usr/local/bin/bench --site site1.local install-app erpnext
2025-05-20 22:28:41,543 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 22:32:36,311 INFO /usr/local/bin/bench --site site1.local console
2025-05-20 22:34:35,240 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 22:34:59,052 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 22:36:14,579 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 22:37:11,873 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 22:37:24,465 INFO /usr/local/bin/bench setup requirements
2025-05-20 22:37:24,497 DEBUG /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-05-20 22:37:30,699 LOG Installing frappe
2025-05-20 22:37:30,703 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/frappe 
2025-05-20 22:37:39,142 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade coverage~=6.5.0 Faker~=18.10.1 pyngrok~=6.0.0 unittest-xml-reporting~=3.2.0 watchdog~=3.0.0 hypothesis~=6.77.0 responses==0.23.1 freezegun~=1.2.2 
2025-05-20 22:37:47,708 DEBUG cd /home/<USER>/frappe-bench/apps/frappe && yarn install --check-files
2025-05-20 22:38:07,756 LOG Installing webshop
2025-05-20 22:38:07,758 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/webshop 
2025-05-20 22:38:16,464 LOG Installing custom_erp
2025-05-20 22:38:16,465 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/custom_erp 
2025-05-20 22:38:25,024 LOG Installing saudi_phase2_api
2025-05-20 22:38:25,027 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/saudi_phase2_api 
2025-05-20 22:38:33,567 LOG Installing payments
2025-05-20 22:38:33,569 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/payments 
2025-05-20 22:38:43,609 LOG Installing zatca_erpgulf
2025-05-20 22:38:43,610 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/zatca_erpgulf 
2025-05-20 22:38:51,691 LOG Installing erpnext
2025-05-20 22:38:51,692 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/erpnext 
2025-05-20 22:39:05,075 DEBUG cd /home/<USER>/frappe-bench/apps/erpnext && yarn install --check-files
2025-05-20 22:39:06,167 LOG Installing smart_theme
2025-05-20 22:39:06,170 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/smart_theme 
2025-05-20 22:39:16,298 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-20 22:39:26,192 INFO /usr/local/bin/bench start
2025-05-20 22:42:46,704 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 22:43:02,047 INFO /usr/local/bin/bench new-app whatsapp_integration
2025-05-20 22:43:02,057 LOG creating new app whatsapp_integration
2025-05-20 22:44:48,828 LOG Installing whatsapp_integration
2025-05-20 22:44:48,858 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/whatsapp_integration 
2025-05-20 22:44:59,543 DEBUG bench build --app whatsapp_integration
2025-05-20 22:45:00,100 INFO /usr/local/bin/bench build --app whatsapp_integration
2025-05-20 22:45:30,225 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-20 22:45:30,963 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-20 22:45:30,963 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-05-20 22:45:32,075 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-20 22:48:58,652 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-20 22:49:12,084 INFO /usr/local/bin/bench restart
2025-05-20 22:49:22,671 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-20 22:49:23,400 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-20 22:49:23,401 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-20 22:49:24,493 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-20 22:49:36,694 INFO /usr/local/bin/bench start
                                                                                                      2025-05-21 12:51:59,440 INFO /usr/local/bin/bench stsrt
2025-05-21 12:52:07,755 INFO /usr/local/bin/bench start
2025-05-21 12:52:09,157 INFO /usr/local/bin/bench schedule
2025-05-21 12:52:09,283 INFO /usr/local/bin/bench worker
2025-05-21 12:52:09,323 INFO /usr/local/bin/bench watch
2025-05-21 12:52:09,493 INFO /usr/local/bin/bench serve --port 8000
2025-05-21 13:08:32,106 INFO /usr/local/bin/bench version
2025-05-21 13:08:43,942 INFO /usr/local/bin/bench list-apps
2025-05-21 13:08:59,933 INFO /usr/local/bin/bench list-sites
2025-05-21 13:09:09,205 INFO /usr/local/bin/bench --site list
2025-05-21 13:13:01,794 INFO /usr/local/bin/bench new-app whatsapp_integration
2025-05-21 13:13:01,833 LOG creating new app whatsapp_integration
2025-05-21 13:13:16,141 WARNING /usr/local/bin/bench new-app whatsapp_integration executed with exit code 1
2025-05-21 13:13:18,479 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-21 13:13:48,300 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-21 13:15:14,724 INFO /usr/local/bin/bench new-app whatsapp_integration
2025-05-21 13:15:14,742 LOG creating new app whatsapp_integration
2025-05-21 13:16:27,998 LOG Installing whatsapp_integration
2025-05-21 13:16:28,017 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/whatsapp_integration 
2025-05-21 13:16:57,194 DEBUG bench build --app whatsapp_integration
2025-05-21 13:16:58,182 INFO /usr/local/bin/bench build --app whatsapp_integration
2025-05-21 13:18:18,693 WARNING /usr/local/bin/bench new-app whatsapp_integration executed with exit code 1
2025-05-21 13:18:20,388 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-21 13:22:28,032 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-21 13:22:57,768 INFO /usr/local/bin/bench setup requirements
2025-05-21 13:22:57,791 DEBUG /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-05-21 13:23:03,741 LOG Installing frappe
2025-05-21 13:23:03,742 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/frappe 
2025-05-21 13:23:19,612 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade coverage~=6.5.0 Faker~=18.10.1 pyngrok~=6.0.0 unittest-xml-reporting~=3.2.0 watchdog~=3.0.0 hypothesis~=6.77.0 responses==0.23.1 freezegun~=1.2.2 
2025-05-21 13:23:34,101 DEBUG cd /home/<USER>/frappe-bench/apps/frappe && yarn install --check-files
2025-05-21 13:24:15,986 LOG Installing webshop
2025-05-21 13:24:15,986 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/webshop 
2025-05-21 13:24:32,999 LOG Installing custom_erp
2025-05-21 13:24:33,013 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/custom_erp 
2025-05-21 13:24:48,148 LOG Installing saudi_phase2_api
2025-05-21 13:24:48,150 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/saudi_phase2_api 
2025-05-21 13:25:04,215 LOG Installing payments
2025-05-21 13:25:04,216 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/payments 
2025-05-21 13:25:23,230 LOG Installing whatsapp_integration
2025-05-21 13:25:23,231 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/whatsapp_integration 
2025-05-21 13:25:40,547 LOG Installing zatca_erpgulf
2025-05-21 13:25:40,614 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/zatca_erpgulf 
2025-05-21 13:25:56,565 LOG Installing erpnext
2025-05-21 13:25:56,566 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/erpnext 
2025-05-21 13:26:13,823 DEBUG cd /home/<USER>/frappe-bench/apps/erpnext && yarn install --check-files
2025-05-21 13:26:16,927 LOG Installing smart_theme
2025-05-21 13:26:16,934 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/smart_theme 
2025-05-21 13:26:39,972 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-21 13:26:53,316 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-21 13:27:07,441 INFO /usr/local/bin/bench restart
2025-05-21 13:27:15,721 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-21 13:27:16,994 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-21 13:27:16,994 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-21 13:27:19,786 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-21 13:27:33,536 INFO /usr/local/bin/bench start
2025-05-21 13:27:43,396 INFO /usr/local/bin/bench --site site1.local add-to-hosts
2025-05-21 13:33:41,917 INFO /usr/local/bin/bench --site site1.local install-app whatsapp_integration
2025-05-21 13:35:41,707 INFO /usr/local/bin/bench new-app whatsapp_integration
2025-05-21 13:35:41,886 LOG creating new app whatsapp_integration
2025-05-21 13:36:47,849 LOG Installing whatsapp_integration
2025-05-21 13:36:47,861 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/whatsapp_integration 
2025-05-21 13:37:03,522 DEBUG bench build --app whatsapp_integration
2025-05-21 13:37:04,197 INFO /usr/local/bin/bench build --app whatsapp_integration
2025-05-21 13:37:26,469 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-21 13:37:27,706 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-21 13:37:27,706 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-05-21 13:37:28,961 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-21 13:39:52,190 INFO /usr/local/bin/bench new-app whatsapp
2025-05-21 13:39:52,222 LOG creating new app whatsapp
2025-05-21 13:41:35,394 LOG Installing whatsapp
2025-05-21 13:41:35,837 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/whatsapp 
2025-05-21 13:41:52,787 DEBUG bench build --app whatsapp
2025-05-21 13:41:53,287 INFO /usr/local/bin/bench build --app whatsapp
2025-05-21 13:42:09,354 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-21 13:42:10,482 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-21 13:42:10,483 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-05-21 13:42:11,798 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-21 13:43:10,521 INFO /usr/local/bin/bench start
2025-05-21 13:43:10,942 INFO /usr/local/bin/bench watch
2025-05-21 13:43:10,951 INFO /usr/local/bin/bench schedule
2025-05-21 13:43:11,007 INFO /usr/local/bin/bench serve --port 8000
2025-05-21 13:43:11,033 INFO /usr/local/bin/bench worker
2025-05-21 14:12:29,796 INFO /usr/local/bin/bench --site site1.local install-app whatsapp
2025-05-21 15:38:41,892 INFO /usr/local/bin/bench restart
2025-05-21 15:38:52,718 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-21 15:38:53,337 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-21 15:38:53,337 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-21 15:38:54,603 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-21 15:39:23,948 INFO /usr/local/bin/bench start
2025-05-24 12:32:06,149 INFO /usr/local/bin/bench start
2025-05-24 12:32:06,617 INFO /usr/local/bin/bench schedule
2025-05-24 12:32:06,701 INFO /usr/local/bin/bench worker
2025-05-24 12:32:06,788 INFO /usr/local/bin/bench watch
2025-05-24 12:32:06,797 INFO /usr/local/bin/bench serve --port 8000
2025-05-24 12:42:24,838 INFO /usr/local/bin/bench start
2025-05-24 12:42:26,918 INFO /usr/local/bin/bench watch
2025-05-24 12:42:27,057 INFO /usr/local/bin/bench serve --port 8000
2025-05-24 12:42:27,068 INFO /usr/local/bin/bench worker
2025-05-24 12:42:27,163 INFO /usr/local/bin/bench schedule
2025-05-24 12:42:53,525 INFO /usr/local/bin/bench start
2025-05-24 12:42:54,536 INFO /usr/local/bin/bench serve --port 8000
2025-05-24 12:42:54,695 INFO /usr/local/bin/bench worker
2025-05-24 12:42:54,696 INFO /usr/local/bin/bench schedule
2025-05-24 12:42:54,922 INFO /usr/local/bin/bench watch
2025-05-24 13:02:42,502 INFO /usr/local/bin/bench start
2025-05-24 13:02:43,565 INFO /usr/local/bin/bench watch
2025-05-24 13:02:43,631 INFO /usr/local/bin/bench serve --port 8000
2025-05-24 13:02:43,656 INFO /usr/local/bin/bench schedule
2025-05-24 13:02:43,694 INFO /usr/local/bin/bench worker
2025-05-24 13:11:38,338 INFO /usr/local/bin/bench start
2025-05-24 13:11:38,685 INFO /usr/local/bin/bench worker
2025-05-24 13:11:38,696 INFO /usr/local/bin/bench watch
2025-05-24 13:11:38,702 INFO /usr/local/bin/bench serve --port 8000
2025-05-24 13:11:39,159 INFO /usr/local/bin/bench schedule
2025-05-24 13:30:42,361 INFO /usr/local/bin/bench build
2025-05-24 13:33:51,603 INFO /usr/local/bin/bench migrate
2025-05-24 13:34:35,257 INFO /usr/local/bin/bench migrate
2025-05-24 13:35:25,735 INFO /usr/local/bin/bench start
2025-05-24 13:35:27,011 INFO /usr/local/bin/bench worker
2025-05-24 13:35:27,018 INFO /usr/local/bin/bench watch
2025-05-24 13:35:27,072 INFO /usr/local/bin/bench schedule
2025-05-24 13:35:27,107 INFO /usr/local/bin/bench serve --port 8000
2025-05-24 13:45:21,939 INFO /usr/local/bin/bench start
2025-05-24 13:45:23,718 INFO /usr/local/bin/bench schedule
2025-05-24 13:45:23,730 INFO /usr/local/bin/bench watch
2025-05-24 13:45:23,731 INFO /usr/local/bin/bench worker
2025-05-24 13:45:23,755 INFO /usr/local/bin/bench serve --port 8000
2025-05-24 13:56:33,683 INFO /usr/local/bin/bench restart
2025-05-24 13:56:42,334 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-24 13:56:43,600 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-24 13:56:43,609 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-24 13:56:45,842 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-24 13:56:57,518 INFO /usr/local/bin/bench restart
2025-05-24 13:56:58,324 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-24 13:56:58,702 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-24 13:56:58,703 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-24 13:57:00,760 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-24 14:13:24,460 INFO /usr/local/bin/bench --site newsmart.local execute frappe.db.get_single_value --args ['WhatsApp Settings', 'whatsapp_server_url']
2025-05-24 14:13:44,392 INFO /usr/local/bin/bench --site newsmart.local list-apps
2025-05-24 14:14:02,514 INFO /usr/local/bin/bench --site newsmart.local install-app whatsapp
2025-05-24 14:14:58,623 INFO /usr/local/bin/bench --site newsmart.local execute frappe.db.get_single_value --args ['WhatsApp Settings', 'whatsapp_server_url']
2025-05-24 14:15:15,256 INFO /usr/local/bin/bench --site newsmart.local execute frappe.db.get_single_value --args ['WhatsApp Settings', 'whatsapp_server_port']
2025-05-24 14:15:31,257 INFO /usr/local/bin/bench --site newsmart.local execute frappe.db.get_single_value --args ['WhatsApp Settings', 'enable_whatsapp']
2025-05-24 14:15:46,935 INFO /usr/local/bin/bench --site newsmart.local execute frappe.db.set_single_value --args ['WhatsApp Settings', 'enable_whatsapp', 1]
2025-05-24 14:16:03,978 INFO /usr/local/bin/bench --site newsmart.local execute whatsapp.whatsapp.api.connect_account --args ['buy']
2025-05-24 14:17:15,338 INFO /usr/local/bin/bench --site site1.local list-apps
2025-05-24 14:17:46,114 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_single_value --args ['WhatsApp Settings', 'enable_whatsapp']
2025-05-24 14:17:59,729 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_single_value --args ['WhatsApp Settings', 'whatsapp_server_url']
2025-05-24 14:18:14,318 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_single_value --args ['WhatsApp Settings', 'whatsapp_server_port']
2025-05-24 14:18:28,238 INFO /usr/local/bin/bench --site site1.local execute whatsapp.whatsapp.api.connect_account --args ['buy']
2025-05-24 14:19:00,249 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_value --args ['WhatsApp Sender Account', 'buy', ['status', 'qr_code_html']]
2025-05-24 14:19:16,631 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_value --args ['WhatsApp Sender Account', 'buy', 'status']
2025-05-24 14:19:33,781 INFO /usr/local/bin/bench --site site1.local migrate
2025-05-24 14:21:02,806 INFO /usr/local/bin/bench --site site1.local execute whatsapp.whatsapp.api.connect_account --args ['buy']
2025-05-24 14:21:33,431 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_value --args ['WhatsApp Sender Account', 'buy', ['status', 'qr_code_html']]
2025-05-24 14:21:50,529 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_value --args ['WhatsApp Sender Account', 'buy', 'status']
2025-05-24 14:22:11,807 INFO /usr/local/bin/bench --site site1.local execute frappe.db.sql --args ['ALTER TABLE  ADD COLUMN qr_code_html LONGTEXT']
2025-05-24 14:22:33,782 INFO /usr/local/bin/bench --site site1.local mariadb
2025-05-24 14:24:38,729 INFO /usr/local/bin/bench --site site1.local reload-doctype WhatsApp Sender Account
2025-05-24 14:24:56,787 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_value --args ['WhatsApp Sender Account', 'buy', ['status', 'qr_code_html']]
2025-05-24 14:25:14,233 INFO /usr/local/bin/bench --site site1.local migrate
2025-05-24 14:26:18,144 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_value --args ['WhatsApp Sender Account', 'buy', ['status', 'qr_code_html']]
2025-05-24 14:26:37,769 INFO /usr/local/bin/bench --site site1.local execute whatsapp.whatsapp.api.connect_account --args ['buy']
2025-05-24 14:31:13,840 INFO /usr/local/bin/bench --site site1.local execute whatsapp.whatsapp.api.connect_account --args ['seling']
2025-05-24 14:32:04,208 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_value --args ['WhatsApp Sender Account', 'seling', 'status']
2025-05-24 14:49:44,130 INFO /usr/local/bin/bench --site site1.local execute whatsapp.whatsapp.api.connect_account --args ['seling']
2025-05-24 14:51:18,496 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_value --args ['WhatsApp Sender Account', 'seling', 'status']
2025-05-24 14:52:35,416 INFO /usr/local/bin/bench --site site1.local execute frappe.get_doc --args ['WhatsApp Sender Account', 'seling']
2025-05-25 13:27:08,362 INFO /usr/local/bin/bench migrate
2025-05-25 13:28:18,927 INFO /usr/local/bin/bench migrate
2025-05-25 13:47:29,007 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 13:49:07,845 INFO /usr/local/bin/bench restart
2025-05-25 13:49:19,399 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-25 13:49:20,957 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-25 13:49:20,959 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-25 13:49:22,756 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-25 13:50:09,857 INFO /usr/local/bin/bench start
2025-05-25 13:54:00,662 INFO /usr/local/bin/bench --site site1.local execute frappe.get_doc --args [{'doctype': 'Sales Invoice', 'customer': 'Test Customer', 'items': [{'item_code': 'Test Item', 'qty': 1, 'rate': 100}]}]
2025-05-25 13:54:36,648 INFO /usr/local/bin/bench --site site1.local execute frappe.get_doc --args [{'doctype': 'Customer', 'customer_name': 'Test Customer', 'mobile_no': '+967782333271'}]
2025-05-25 13:55:07,821 INFO /usr/local/bin/bench --site site1.local execute 
import frappe
customer = frappe.new_doc('Customer')
customer.customer_name = 'Test Customer'
customer.mobile_no = '+967782333271'
customer.save()
print('Customer created:', customer.name)

2025-05-25 14:02:18,289 INFO /usr/local/bin/bench clear-cache
2025-05-25 14:05:56,920 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 14:07:06,213 INFO /usr/local/bin/bench clear-cache
2025-05-25 14:08:29,859 INFO /usr/local/bin/bench build --app whatsapp --force
2025-05-25 14:11:15,145 INFO /usr/local/bin/bench start
2025-05-25 14:11:20,084 INFO /usr/local/bin/bench schedule
2025-05-25 14:11:20,238 INFO /usr/local/bin/bench worker
2025-05-25 14:11:20,246 INFO /usr/local/bin/bench watch
2025-05-25 14:11:20,270 INFO /usr/local/bin/bench serve --port 8000
2025-05-25 14:16:26,312 INFO /usr/local/bin/bench build --app whatsapp --force
2025-05-25 14:38:42,433 INFO /usr/local/bin/bench start
2025-05-25 14:38:44,017 INFO /usr/local/bin/bench schedule
2025-05-25 14:38:44,047 INFO /usr/local/bin/bench serve --port 8000
2025-05-25 14:38:44,170 INFO /usr/local/bin/bench watch
2025-05-25 14:38:44,222 INFO /usr/local/bin/bench worker
2025-05-25 14:43:34,306 INFO /usr/local/bin/bench start
2025-05-25 14:43:35,013 INFO /usr/local/bin/bench serve --port 8000
2025-05-25 14:43:35,163 INFO /usr/local/bin/bench worker
2025-05-25 14:43:35,222 INFO /usr/local/bin/bench schedule
2025-05-25 14:43:35,344 INFO /usr/local/bin/bench watch
2025-05-25 14:45:23,631 INFO /usr/local/bin/bench build
2025-05-25 14:46:16,388 INFO /usr/local/bin/bench start
2025-05-25 14:46:16,866 INFO /usr/local/bin/bench watch
2025-05-25 14:46:16,879 INFO /usr/local/bin/bench worker
2025-05-25 14:46:16,924 INFO /usr/local/bin/bench schedule
2025-05-25 14:46:16,952 INFO /usr/local/bin/bench serve --port 8000
2025-05-25 14:49:25,224 INFO /usr/local/bin/bench migrate
2025-05-25 14:51:07,037 INFO /usr/local/bin/bench redis_cashe
2025-05-25 14:51:20,414 INFO /usr/local/bin/bench redis cashe
2025-05-25 14:51:32,873 INFO /usr/local/bin/bench start
2025-05-25 14:51:33,262 INFO /usr/local/bin/bench serve --port 8000
2025-05-25 14:51:33,263 INFO /usr/local/bin/bench watch
2025-05-25 14:51:33,264 INFO /usr/local/bin/bench worker
2025-05-25 14:51:33,507 INFO /usr/local/bin/bench schedule
2025-05-25 14:53:07,896 INFO /usr/local/bin/bench --site site1.local console
2025-05-25 14:56:09,386 INFO /usr/local/bin/bench start
2025-05-25 14:56:10,306 INFO /usr/local/bin/bench serve --port 8000
2025-05-25 14:56:10,328 INFO /usr/local/bin/bench worker
2025-05-25 14:56:10,343 INFO /usr/local/bin/bench schedule
2025-05-25 14:56:10,660 INFO /usr/local/bin/bench watch
2025-05-25 15:11:51,373 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 15:12:16,246 INFO /usr/local/bin/bench restart
2025-05-25 15:12:24,328 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-25 15:12:25,318 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-25 15:12:25,319 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-25 15:12:29,240 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-25 15:12:56,304 INFO /usr/local/bin/bench start
2025-05-25 15:13:19,328 INFO /usr/local/bin/bench --site newsmart.local clear-cache
2025-05-25 15:16:31,476 INFO /usr/local/bin/bench --site newsmart.local list-apps
2025-05-25 15:16:33,134 INFO /usr/local/bin/bench --site newsmart.local execute frappe.db.get_single_value --args ['WhatsApp Settings', 'enable_whatsapp']
2025-05-25 15:16:34,745 INFO /usr/local/bin/bench --site newsmart.local execute frappe.get_meta --args ['WhatsApp Sender Account']
2025-05-25 15:16:36,735 INFO /usr/local/bin/bench --site newsmart.local execute frappe.get_meta --args ['WhatsApp Message Log']
2025-05-25 15:16:39,203 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 15:25:15,066 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 15:25:41,638 INFO /usr/local/bin/bench --site newsmart.local clear-cache
2025-05-25 15:26:42,705 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-25 15:27:05,740 INFO /usr/local/bin/bench restart
2025-05-25 15:27:07,357 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-25 15:27:08,280 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-25 15:27:08,281 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-25 15:27:10,885 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-25 15:27:30,002 INFO /usr/local/bin/bench start
2025-05-25 15:29:15,686 INFO /usr/local/bin/bench start
2025-05-25 15:29:16,229 INFO /usr/local/bin/bench worker
2025-05-25 15:29:16,325 INFO /usr/local/bin/bench schedule
2025-05-25 15:29:16,358 INFO /usr/local/bin/bench watch
2025-05-25 15:29:16,379 INFO /usr/local/bin/bench serve --port 8000
2025-05-25 15:32:11,464 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 15:32:21,243 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-25 15:58:21,703 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 15:58:56,953 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-25 16:04:46,431 INFO /usr/local/bin/bench --site site1.local console
2025-05-25 16:07:02,048 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 16:07:19,516 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-25 16:14:22,822 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 16:14:28,543 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-25 16:18:36,025 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 16:19:20,368 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 16:19:48,389 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-25 17:02:03,904 INFO /usr/local/bin/bench build --app whatsapp
2025-05-25 17:02:22,876 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 13:01:18,833 INFO /usr/local/bin/bench start
2025-05-26 13:01:20,664 INFO /usr/local/bin/bench schedule
2025-05-26 13:01:20,683 INFO /usr/local/bin/bench serve --port 8000
2025-05-26 13:01:20,684 INFO /usr/local/bin/bench worker
2025-05-26 13:01:20,898 INFO /usr/local/bin/bench watch
2025-05-26 13:09:31,294 INFO /usr/local/bin/bench new-app interface_customization
2025-05-26 13:09:31,333 LOG creating new app interface_customization
2025-05-26 13:11:06,614 LOG Installing interface_customization
2025-05-26 13:11:06,621 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/interface_customization 
2025-05-26 13:11:18,501 DEBUG bench build --app interface_customization
2025-05-26 13:11:18,885 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 13:11:45,273 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-26 13:11:46,969 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-26 13:11:46,970 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-05-26 13:11:50,439 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-26 13:11:58,422 INFO /usr/local/bin/bench --site newsmart.local install-app interface_customization
2025-05-26 13:12:46,160 INFO /usr/local/bin/bench --site site1.local install-app interface_customization
2025-05-26 13:20:11,263 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 13:20:27,723 INFO /usr/local/bin/bench restart
2025-05-26 13:20:34,925 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-26 13:20:35,393 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-26 13:20:35,393 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-26 13:20:36,374 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-26 13:20:49,239 INFO /usr/local/bin/bench start
2025-05-26 13:20:50,347 INFO /usr/local/bin/bench serve --port 8000
2025-05-26 13:20:50,366 INFO /usr/local/bin/bench watch
2025-05-26 13:20:50,408 INFO /usr/local/bin/bench worker
2025-05-26 13:20:50,432 INFO /usr/local/bin/bench schedule
2025-05-26 13:21:10,964 INFO /usr/local/bin/bench --site site1.local migrate
2025-05-26 13:26:15,462 INFO /usr/local/bin/bench --site site1.local execute interface_customization.install.after_install
2025-05-26 13:26:42,517 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 13:27:10,277 INFO /usr/local/bin/bench start
2025-05-26 13:49:44,025 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 13:50:26,486 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 14:09:30,050 INFO /usr/local/bin/bench start
2025-05-26 14:09:38,811 INFO /usr/local/bin/bench start
2025-05-26 14:29:58,770 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 14:30:26,399 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 14:51:37,302 INFO /usr/local/bin/bench start
2025-05-26 14:51:37,997 INFO /usr/local/bin/bench watch
2025-05-26 14:51:38,012 INFO /usr/local/bin/bench serve --port 8000
2025-05-26 14:51:38,085 INFO /usr/local/bin/bench worker
2025-05-26 14:51:38,352 INFO /usr/local/bin/bench schedule
2025-05-26 14:58:23,016 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 14:58:51,237 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 15:02:24,147 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 15:02:50,888 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 15:19:09,751 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 15:19:29,924 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 15:29:11,088 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 15:29:37,254 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 15:31:10,041 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 15:31:36,362 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 15:32:32,446 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 15:33:02,001 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 15:41:07,704 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 15:41:40,525 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 16:42:47,667 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 16:43:12,259 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 16:48:41,994 INFO /usr/local/bin/bench --site site1.local console
2025-05-26 17:02:02,742 INFO /usr/local/bin/bench start
2025-05-26 17:02:03,866 INFO /usr/local/bin/bench watch
2025-05-26 17:02:03,906 INFO /usr/local/bin/bench worker
2025-05-26 17:02:03,990 INFO /usr/local/bin/bench serve --port 8000
2025-05-26 17:02:04,092 INFO /usr/local/bin/bench schedule
2025-05-26 17:11:06,595 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 17:12:01,506 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 17:18:57,310 INFO /usr/local/bin/bench build --app interface_customization
2025-05-26 17:19:40,057 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-26 17:20:17,619 INFO /usr/local/bin/bench restart
2025-05-26 17:20:25,465 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-26 17:20:25,999 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-26 17:20:26,000 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-26 17:20:27,641 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-26 17:27:28,840 INFO /usr/local/bin/bench start --skip-redis-config-generation
2025-05-26 17:27:28,870 WARNING /usr/local/bin/bench start --skip-redis-config-generation executed with exit code 2
2025-05-26 17:27:31,540 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-26 17:27:48,559 INFO /usr/local/bin/bench start
2025-05-27 12:45:17,300 INFO /usr/local/bin/bench start
2025-05-27 12:45:17,820 INFO /usr/local/bin/bench schedule
2025-05-27 12:45:17,887 INFO /usr/local/bin/bench serve --port 8000
2025-05-27 12:45:17,983 INFO /usr/local/bin/bench worker
2025-05-27 12:45:18,056 INFO /usr/local/bin/bench watch
2025-05-27 12:55:16,390 INFO /usr/local/bin/bench build --app interface_customization
2025-05-27 12:56:00,059 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-27 13:05:49,076 INFO /usr/local/bin/bench build --app interface_customization
2025-05-27 13:06:11,432 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-27 13:18:28,252 INFO /usr/local/bin/bench build --app interface_customization
2025-05-27 13:18:54,630 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-05-27 16:51:13,661 INFO /usr/local/bin/bench start
2025-05-27 16:51:14,366 INFO /usr/local/bin/bench worker
2025-05-27 16:51:14,376 INFO /usr/local/bin/bench watch
2025-05-27 16:51:14,383 INFO /usr/local/bin/bench schedule
2025-05-27 16:51:14,436 INFO /usr/local/bin/bench serve --port 8000
2025-05-28 14:22:08,673 INFO /usr/local/bin/bench start
2025-05-28 14:22:11,749 INFO /usr/local/bin/bench serve --port 8000
2025-05-28 14:22:11,848 INFO /usr/local/bin/bench schedule
2025-05-28 14:22:12,056 INFO /usr/local/bin/bench worker
2025-05-28 14:22:12,382 INFO /usr/local/bin/bench watch
2025-05-28 14:31:07,961 INFO /usr/local/bin/bench start
2025-05-28 14:31:09,182 INFO /usr/local/bin/bench watch
2025-05-28 14:31:09,221 INFO /usr/local/bin/bench serve --port 8000
2025-05-28 14:31:09,331 INFO /usr/local/bin/bench worker
2025-05-28 14:31:09,387 INFO /usr/local/bin/bench schedule
2025-05-28 14:52:42,889 INFO /usr/local/bin/bench restart
2025-05-28 14:52:50,884 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-28 14:52:51,765 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-28 14:52:51,768 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-28 14:52:54,237 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-28 14:53:08,351 INFO /usr/local/bin/bench clear-cache
2025-05-28 14:55:12,963 INFO /usr/local/bin/bench start
2025-05-28 14:55:13,405 INFO /usr/local/bin/bench watch
2025-05-28 14:55:13,447 INFO /usr/local/bin/bench serve --port 8000
2025-05-28 14:55:13,467 INFO /usr/local/bin/bench schedule
2025-05-28 14:55:13,505 INFO /usr/local/bin/bench worker
2025-05-28 14:57:25,437 INFO /usr/local/bin/bench start
2025-05-28 14:57:25,785 INFO /usr/local/bin/bench serve --port 8000
2025-05-28 14:57:25,836 INFO /usr/local/bin/bench worker
2025-05-28 14:57:25,884 INFO /usr/local/bin/bench watch
2025-05-28 14:57:25,886 INFO /usr/local/bin/bench schedule
2025-05-28 14:59:05,597 INFO /usr/local/bin/bench start
2025-05-28 15:00:54,959 INFO /usr/local/bin/bench build --app interface_customization
2025-05-28 15:01:51,816 INFO /usr/local/bin/bench clear-cache
2025-05-28 15:01:57,589 INFO /usr/local/bin/bench restart
2025-05-28 15:01:59,075 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-28 15:01:59,899 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-28 15:01:59,899 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-28 15:02:00,953 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-28 15:02:20,137 INFO /usr/local/bin/bench --site all clear-cache
2025-05-28 15:02:46,637 INFO /usr/local/bin/bench start
2025-05-28 15:05:17,067 INFO /usr/local/bin/bench --site all clear-website-cache
2025-05-28 15:05:42,716 INFO /usr/local/bin/bench start --skip-redis-config-generation
2025-05-28 15:05:42,864 WARNING /usr/local/bin/bench start --skip-redis-config-generation executed with exit code 2
2025-05-28 15:05:45,489 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-28 15:06:16,912 INFO /usr/local/bin/bench status
2025-05-28 15:06:50,483 INFO /usr/local/bin/bench --site all list-apps
2025-05-31 13:18:41,662 INFO /usr/local/bin/bench start
2025-05-31 13:18:43,142 INFO /usr/local/bin/bench schedule
2025-05-31 13:18:43,193 INFO /usr/local/bin/bench serve --port 8000
2025-05-31 13:18:43,309 INFO /usr/local/bin/bench watch
2025-05-31 13:18:43,443 INFO /usr/local/bin/bench worker
2025-05-31 13:37:36,000 INFO /usr/local/bin/bench start
2025-05-31 13:37:36,914 INFO /usr/local/bin/bench worker
2025-05-31 13:37:36,918 INFO /usr/local/bin/bench schedule
2025-05-31 13:37:36,950 INFO /usr/local/bin/bench watch
2025-05-31 13:37:37,065 INFO /usr/local/bin/bench serve --port 8000
2025-05-31 14:03:49,355 INFO /usr/local/bin/bench --site site1.local migrate
2025-05-31 14:05:16,543 INFO /usr/local/bin/bench restart
2025-05-31 14:05:30,778 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-31 14:05:32,774 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-31 14:05:32,776 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-31 14:05:40,076 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-31 14:05:51,824 INFO /usr/local/bin/bench start
2025-05-31 14:06:54,220 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_value --args ['DocType', 'Download Backup', 'name']
2025-05-31 14:07:16,025 INFO /usr/local/bin/bench --site site1.local execute frappe.get_doc --args {'doctype': 'Download Backup', 'enable_auto_backup': 0, 'backup_count': 7, 'backup_format': 'SQL'}
2025-05-31 14:07:56,542 INFO /usr/local/bin/bench --site site1.local console
2025-05-31 14:35:46,661 INFO /usr/local/bin/bench start
2025-05-31 14:35:47,576 INFO /usr/local/bin/bench serve --port 8000
2025-05-31 14:35:47,616 INFO /usr/local/bin/bench watch
2025-05-31 14:35:47,628 INFO /usr/local/bin/bench worker
2025-05-31 14:35:48,000 INFO /usr/local/bin/bench schedule
2025-05-31 14:38:33,154 INFO /usr/local/bin/bench start
2025-05-31 14:38:34,037 INFO /usr/local/bin/bench serve --port 8000
2025-05-31 14:38:34,210 INFO /usr/local/bin/bench worker
2025-05-31 14:38:34,224 INFO /usr/local/bin/bench schedule
2025-05-31 14:38:34,674 INFO /usr/local/bin/bench watch
2025-05-31 14:41:11,214 INFO /usr/local/bin/bench start
2025-05-31 14:41:12,952 INFO /usr/local/bin/bench schedule
2025-05-31 14:41:13,018 INFO /usr/local/bin/bench serve --port 8000
2025-05-31 14:41:13,053 INFO /usr/local/bin/bench watch
2025-05-31 14:41:13,160 INFO /usr/local/bin/bench worker
2025-05-31 14:49:20,158 INFO /usr/local/bin/bench start
2025-05-31 14:49:21,151 INFO /usr/local/bin/bench schedule
2025-05-31 14:49:21,193 INFO /usr/local/bin/bench watch
2025-05-31 14:49:21,216 INFO /usr/local/bin/bench worker
2025-05-31 14:49:21,478 INFO /usr/local/bin/bench serve --port 8000
2025-05-31 14:52:50,013 INFO /usr/local/bin/bench start
2025-05-31 14:52:51,232 INFO /usr/local/bin/bench watch
2025-05-31 14:52:51,492 INFO /usr/local/bin/bench schedule
2025-05-31 14:52:51,544 INFO /usr/local/bin/bench serve --port 8000
2025-05-31 14:52:51,545 INFO /usr/local/bin/bench worker
2025-05-31 15:08:49,020 INFO /usr/local/bin/bench build --app interface_customization
2025-05-31 15:09:50,280 INFO /usr/local/bin/bench restart
2025-05-31 15:10:20,207 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-05-31 15:10:20,994 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-05-31 15:10:20,995 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-05-31 15:10:24,589 INFO A newer version of bench is available: 5.22.3 → 5.25.1
2025-05-31 15:15:58,890 INFO /usr/local/bin/bench build --app interface_customization
2025-05-31 15:17:00,058 INFO /usr/local/bin/bench clear-cache
2025-05-31 15:17:02,209 INFO /usr/local/bin/bench clear-website-cache
2025-05-31 15:26:54,899 INFO /usr/local/bin/bench build --app interface_customization
2025-05-31 15:40:59,170 INFO /usr/local/bin/bench build --app interface_customization
2025-05-31 16:52:58,512 INFO /usr/local/bin/bench build --app interface_customization
2025-05-31 16:58:28,525 INFO /usr/local/bin/bench build --app interface_customization
2025-05-31 16:59:38,412 INFO /usr/local/bin/bench start
2025-05-31 16:59:39,183 INFO /usr/local/bin/bench schedule
2025-05-31 16:59:39,200 INFO /usr/local/bin/bench worker
2025-05-31 16:59:39,220 INFO /usr/local/bin/bench serve --port 8000
2025-05-31 16:59:39,264 INFO /usr/local/bin/bench watch
2025-06-01 12:37:54,829 INFO /usr/local/bin/bench start
2025-06-01 12:37:55,550 INFO /usr/local/bin/bench serve --port 8000
2025-06-01 12:37:55,555 INFO /usr/local/bin/bench schedule
2025-06-01 12:37:55,699 INFO /usr/local/bin/bench watch
2025-06-01 12:37:55,822 INFO /usr/local/bin/bench worker
2025-06-01 13:11:44,961 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 13:16:59,473 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 13:29:56,074 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 13:37:52,516 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 13:39:43,283 INFO /usr/local/bin/bench clear-cache
2025-06-01 14:07:27,741 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 14:08:06,812 INFO /usr/local/bin/bench clear-cache
2025-06-01 14:14:37,639 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 14:14:46,695 INFO /usr/local/bin/bench clear-cache
2025-06-01 14:32:18,873 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 14:32:25,686 INFO /usr/local/bin/bench clear-cache
2025-06-01 14:55:52,071 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 14:56:17,014 INFO /usr/local/bin/bench clear-cache
2025-06-01 15:00:27,641 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 15:08:52,575 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 15:14:46,749 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 15:21:38,888 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 15:33:16,830 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 15:39:21,893 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 15:42:18,144 INFO /usr/local/bin/bench start
2025-06-01 15:42:18,935 INFO /usr/local/bin/bench schedule
2025-06-01 15:42:18,986 INFO /usr/local/bin/bench worker
2025-06-01 15:42:19,043 INFO /usr/local/bin/bench serve --port 8000
2025-06-01 15:42:19,143 INFO /usr/local/bin/bench watch
2025-06-01 15:51:02,067 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 16:02:25,578 INFO /usr/local/bin/bench start
2025-06-01 16:02:26,265 INFO /usr/local/bin/bench serve --port 8000
2025-06-01 16:02:26,411 INFO /usr/local/bin/bench watch
2025-06-01 16:02:26,449 INFO /usr/local/bin/bench worker
2025-06-01 16:02:26,574 INFO /usr/local/bin/bench schedule
2025-06-01 16:05:37,274 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 16:06:21,889 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-06-01 16:15:24,769 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 16:17:07,282 INFO /usr/local/bin/bench build --app interface_customization
2025-06-01 16:17:19,956 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-06-25 16:39:48,641 INFO /usr/local/bin/bench start
2025-06-25 16:39:49,656 INFO /usr/local/bin/bench worker
2025-06-25 16:39:49,689 INFO /usr/local/bin/bench schedule
2025-06-25 16:39:49,698 INFO /usr/local/bin/bench serve --port 8000
2025-06-25 16:39:49,725 INFO /usr/local/bin/bench watch
2025-06-25 16:52:52,043 INFO /usr/local/bin/bench new-app erpsmart_car --description نظام إدارة معرض السيارات المتكامل مع ERPNext
2025-06-25 16:52:52,345 WARNING /usr/local/bin/bench new-app erpsmart_car --description نظام إدارة معرض السيارات المتكامل مع ERPNext executed with exit code 2
2025-06-25 16:52:54,922 INFO A newer version of bench is available: 5.22.3 → 5.25.7
2025-06-25 16:53:21,898 INFO /usr/local/bin/bench new-app erpsmart_car
2025-06-25 16:53:21,923 LOG creating new app erpsmart_car
2025-06-25 16:55:31,857 LOG Installing erpsmart_car
2025-06-25 16:55:31,864 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/erpsmart_car 
2025-06-25 16:55:58,330 DEBUG bench build --app erpsmart_car
2025-06-25 16:55:59,111 INFO /usr/local/bin/bench build --app erpsmart_car
2025-06-25 16:56:58,233 INFO /usr/local/bin/bench new-app erpsmart_car
2025-06-25 16:56:58,251 LOG creating new app erpsmart_car
2025-06-25 16:58:12,318 LOG Installing erpsmart_car
2025-06-25 16:58:12,322 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/erpsmart_car 
2025-06-25 16:58:20,327 DEBUG bench build --app erpsmart_car
2025-06-25 16:58:20,572 INFO /usr/local/bin/bench build --app erpsmart_car
2025-06-25 16:58:29,312 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-06-25 16:58:29,902 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-25 16:58:29,902 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-25 16:58:31,101 INFO A newer version of bench is available: 5.22.3 → 5.25.7
2025-06-25 17:22:13,611 INFO /usr/local/bin/bench --site site1.local install-app erpsmart_car
2025-06-25 17:22:41,720 INFO /usr/local/bin/bench get-app file:///home/<USER>/frappe-bench/apps/erpsmart_car
2025-06-25 17:22:50,463 INFO App moved from apps/erpsmart_car to archived/apps/erpsmart_car-2025-06-25
2025-06-25 17:22:50,476 LOG Getting erpsmart_car
2025-06-25 17:22:50,477 DEBUG cd ./apps && git clone https://github.com//home/<USER>/frappe-bench/apps/erpsmart_car.git  --depth 1 --origin upstream
2025-06-25 17:22:52,116 WARNING cd ./apps && git clone https://github.com//home/<USER>/frappe-bench/apps/erpsmart_car.git  --depth 1 --origin upstream executed with exit code 128
2025-06-25 17:22:52,116 WARNING /usr/local/bin/bench get-app file:///home/<USER>/frappe-bench/apps/erpsmart_car executed with exit code 1
2025-06-25 17:22:53,128 INFO A newer version of bench is available: 5.22.3 → 5.25.7
2025-06-25 17:27:42,667 INFO /usr/local/bin/bench new-app erpsmart_car
2025-06-25 17:27:42,675 LOG creating new app erpsmart_car
2025-06-25 17:27:43,391 WARNING /usr/local/bin/bench new-app erpsmart_car executed with exit code 1
2025-06-25 17:27:44,164 INFO A newer version of bench is available: 5.22.3 → 5.25.7
2025-06-25 17:29:21,115 INFO /usr/local/bin/bench start
2025-06-25 17:29:21,978 INFO /usr/local/bin/bench watch
2025-06-25 17:29:21,983 INFO /usr/local/bin/bench serve --port 8000
2025-06-25 17:29:22,296 INFO /usr/local/bin/bench schedule
2025-06-25 17:29:22,477 INFO /usr/local/bin/bench worker
2025-06-25 17:29:26,866 INFO /usr/local/bin/bench start
2025-06-25 17:29:27,345 INFO /usr/local/bin/bench serve --port 8000
2025-06-25 17:29:27,457 INFO /usr/local/bin/bench schedule
2025-06-25 17:29:27,494 INFO /usr/local/bin/bench worker
2025-06-25 17:29:27,497 INFO /usr/local/bin/bench watch
2025-06-25 18:00:01,854 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-25 18:07:46,596 INFO /usr/local/bin/bench --site site1.local install-app erpsmart_car
2025-06-25 18:08:27,805 INFO /usr/local/bin/bench --site site1.local console
2025-06-25 18:11:24,741 INFO /usr/local/bin/bench start
2025-06-25 18:11:25,815 INFO /usr/local/bin/bench serve --port 8000
2025-06-25 18:11:25,924 INFO /usr/local/bin/bench schedule
2025-06-25 18:11:26,009 INFO /usr/local/bin/bench watch
2025-06-25 18:11:26,019 INFO /usr/local/bin/bench worker
2025-06-25 18:12:12,921 INFO /usr/local/bin/bench --site site1.local migrate
2025-06-25 18:18:23,429 INFO /usr/local/bin/bench --site site1.local install-app erpsmart_car
2025-06-25 18:19:17,098 INFO /usr/local/bin/bench --site site1.local install-app erpsmart_car --force
2025-06-25 18:20:06,577 INFO /usr/local/bin/bench --site site1.local --force install-app erpsmart_car
2025-06-25 18:22:21,963 INFO /usr/local/bin/bench --site site1.local mariadb
2025-06-25 19:46:36,163 INFO /usr/local/bin/bench start
2025-06-25 19:46:36,639 INFO /usr/local/bin/bench serve --port 8000
2025-06-25 19:46:36,755 INFO /usr/local/bin/bench schedule
2025-06-25 19:46:36,764 INFO /usr/local/bin/bench watch
2025-06-25 19:46:36,792 INFO /usr/local/bin/bench worker
2025-06-25 19:55:22,327 INFO /usr/local/bin/bench --site site1.local install-app custom_erp
2025-06-25 20:06:04,369 INFO /usr/local/bin/bench --site site1.local list-apps
2025-06-25 20:06:32,580 INFO /usr/local/bin/bench --site site1.local list-apps
2025-06-25 21:56:34,223 INFO /usr/local/bin/bench --site site1.local install-app erpsmart_car --force
2025-06-26 13:51:53,664 INFO /usr/local/bin/bench start
2025-06-26 13:51:56,025 INFO /usr/local/bin/bench worker
2025-06-26 13:51:56,085 INFO /usr/local/bin/bench serve --port 8000
2025-06-26 13:51:56,153 INFO /usr/local/bin/bench schedule
2025-06-26 13:51:56,307 INFO /usr/local/bin/bench watch
2025-06-26 14:58:07,143 INFO /usr/local/bin/bench --site site1.local install-app custom_erp
2025-06-26 14:59:58,828 INFO /usr/local/bin/bench --version
2025-06-26 15:00:23,968 INFO /usr/local/bin/bench get-app custom_erp
2025-06-26 15:02:19,622 LOG Installing custom_erp
2025-06-26 15:02:19,624 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/custom_erp 
2025-06-26 15:02:39,468 DEBUG bench build --app custom_erp
2025-06-26 15:02:40,489 INFO /usr/local/bin/bench build --app custom_erp
2025-06-26 15:04:18,785 INFO /usr/local/bin/bench --site site1.local list-apps
2025-06-26 18:31:55,147 INFO /usr/local/bin/bench start
2025-06-26 18:31:56,078 INFO /usr/local/bin/bench serve --port 8000
2025-06-26 18:31:56,084 INFO /usr/local/bin/bench watch
2025-06-26 18:31:56,121 INFO /usr/local/bin/bench schedule
2025-06-26 18:31:56,167 INFO /usr/local/bin/bench worker
2025-06-26 18:36:37,998 INFO /usr/local/bin/bench --site site1.local migrate
2025-06-26 18:46:25,704 INFO /usr/local/bin/bench --site site1.local install-app erpsmart_car
2025-06-26 18:48:02,373 INFO /usr/local/bin/bench --site site1.local uninstall-app erpsmart_car
2025-06-26 19:12:00,886 INFO /usr/local/bin/bench --site newsmart.local migrate
2025-06-26 19:14:22,927 INFO /usr/local/bin/bench --site site1.local migrate
2025-06-26 19:16:13,084 INFO /usr/local/bin/bench --site site1.local list-apps
2025-06-26 19:17:10,945 INFO /usr/local/bin/bench get-app --help
2025-06-26 19:17:13,213 INFO A newer version of bench is available: 5.22.3 → 5.25.7
2025-06-26 19:17:50,370 INFO /usr/local/bin/bench install-app custom_erp --site site1.local
2025-06-26 19:18:38,591 INFO /usr/local/bin/bench --site site1.local install-app custom_erp
2025-06-26 19:30:19,950 INFO /usr/local/bin/bench --site site1.local migrate
2025-06-26 19:33:22,698 INFO /usr/local/bin/bench --site site1.local migrate
2025-06-26 19:53:37,504 INFO /usr/local/bin/bench --site site1.local migrate
2025-06-26 20:07:00,645 INFO /usr/local/bin/bench --site site1.local migrate
2025-06-26 20:18:18,766 INFO /usr/local/bin/bench --site site1.local migrate
2025-06-26 20:43:42,056 INFO /usr/local/bin/bench --site site1.local migrate
2025-06-26 21:31:33,685 INFO /usr/local/bin/bench --site site1.local migrate
2025-06-26 21:50:50,474 INFO /usr/local/bin/bench --site site1.local list-apps
2025-06-26 21:52:26,637 INFO /usr/local/bin/bench --site site1.local migrate
2025-06-26 21:55:09,629 INFO /usr/local/bin/bench --site site1.local console --execute frappe.db.delete('Dashboard Chart', {'name': 'Monthly Car Sales'}); frappe.db.delete('Dashboard Chart', {'name': 'Car Sales by Brand'}); frappe.db.commit()
2025-06-26 21:55:29,477 INFO /usr/local/bin/bench --site site1.local console
2025-06-27 13:25:23,948 INFO /usr/local/bin/bench start
2025-06-27 13:25:25,437 INFO /usr/local/bin/bench worker
2025-06-27 13:25:25,442 INFO /usr/local/bin/bench watch
2025-06-27 13:25:25,475 INFO /usr/local/bin/bench schedule
2025-06-27 13:25:25,500 INFO /usr/local/bin/bench serve --port 8000
2025-06-27 13:51:04,487 INFO /usr/local/bin/bench migrate
2025-06-27 14:02:56,876 INFO /usr/local/bin/bench new-app customsmart
2025-06-27 14:02:57,037 LOG creating new app customsmart
2025-06-27 14:04:22,256 LOG Installing customsmart
2025-06-27 14:04:22,410 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/customsmart 
2025-06-27 14:05:00,747 DEBUG bench build --app customsmart
2025-06-27 14:05:03,577 INFO /usr/local/bin/bench build --app customsmart
2025-06-27 14:07:21,674 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-06-27 14:07:24,290 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-27 14:07:24,290 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-27 14:07:28,192 INFO A newer version of bench is available: 5.22.3 → 5.25.7
2025-06-27 14:11:47,622 INFO /usr/local/bin/bench start
2025-06-27 14:11:48,424 INFO /usr/local/bin/bench schedule
2025-06-27 14:11:48,433 INFO /usr/local/bin/bench worker
2025-06-27 14:11:48,509 INFO /usr/local/bin/bench serve --port 8000
2025-06-27 14:11:48,510 INFO /usr/local/bin/bench watch
2025-06-27 14:35:13,555 INFO /usr/local/bin/bench --site site1.local install-app customsmart
2025-06-27 14:43:31,789 INFO /usr/local/bin/bench --site newsmart.local execute customsmart.install_car_showroom.install_car_showroom_customizations
2025-06-27 14:45:01,878 INFO /usr/local/bin/bench --site site1.local execute customsmart.install_car_showroom.install_car_showroom_customizations
2025-06-27 14:45:45,508 INFO /usr/local/bin/bench --site site1.local export-fixtures
2025-06-27 14:46:17,872 INFO /usr/local/bin/bench --site site1.local export-fixtures --app customsmart
2025-06-27 14:47:44,170 INFO /usr/local/bin/bench start
2025-06-27 14:47:45,651 INFO /usr/local/bin/bench worker
2025-06-27 14:47:45,717 INFO /usr/local/bin/bench watch
2025-06-27 14:47:45,831 INFO /usr/local/bin/bench schedule
2025-06-27 14:47:45,844 INFO /usr/local/bin/bench serve --port 8000
2025-06-27 14:52:22,926 INFO /usr/local/bin/bench --site site1.local console
2025-06-27 14:56:16,701 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_list --args Custom Field --kwargs {'filters': {'dt': ['in', ['Sales Invoice', 'Sales Invoice Item']]}, 'fields': ['name', 'fieldname', 'label']}
2025-06-27 14:57:22,479 INFO /usr/local/bin/bench --site site1.local mariadb -e SELECT name, fieldname, label FROM `tabCustom Field` WHERE dt IN ('Sales Invoice', 'Sales Invoice Item') ORDER BY dt, idx;
2025-06-27 14:58:16,888 INFO /usr/local/bin/bench --site site1.local list-apps
2025-06-27 15:00:12,416 INFO /usr/local/bin/bench version
2025-06-27 15:01:18,787 INFO /usr/local/bin/bench --site site1.local migrate
2025-06-27 15:16:40,668 INFO /usr/local/bin/bench --site site1.local execute customsmart.install_car_showroom.install_car_showroom_customizations
2025-06-27 15:17:40,178 INFO /usr/local/bin/bench --site site1.local export-fixtures --app customsmart
2025-06-27 15:23:06,849 INFO /usr/local/bin/bench --site site1.local uninstall-app custom_erp
2025-06-27 15:30:15,631 INFO /usr/local/bin/bench --site site1.local execute customsmart.car_record_doctype.install_car_doctypes
2025-06-27 15:31:01,523 INFO /usr/local/bin/bench --site site1.local execute customsmart.create_module.create_custom_smart_module
2025-06-27 15:32:58,659 INFO /usr/local/bin/bench --site site1.local execute customsmart.car_record_doctype.install_car_doctypes
2025-06-27 15:33:27,567 INFO /usr/local/bin/bench --site site1.local console
2025-06-27 15:38:52,511 INFO /usr/local/bin/bench start
2025-06-27 15:38:53,385 INFO /usr/local/bin/bench serve --port 8000
2025-06-27 15:38:53,406 INFO /usr/local/bin/bench watch
2025-06-27 15:38:53,410 INFO /usr/local/bin/bench schedule
2025-06-27 15:38:53,460 INFO /usr/local/bin/bench worker
2025-06-27 18:44:24,305 INFO /usr/local/bin/bench start
2025-06-27 18:44:25,494 INFO /usr/local/bin/bench worker
2025-06-27 18:44:25,495 INFO /usr/local/bin/bench schedule
2025-06-27 18:44:25,506 INFO /usr/local/bin/bench serve --port 8000
2025-06-27 18:44:25,537 INFO /usr/local/bin/bench watch
2025-06-27 19:12:00,079 INFO /usr/local/bin/bench --site site1.local execute customsmart.car_record_doctype.install_car_doctypes
2025-06-27 19:14:44,793 INFO /usr/local/bin/bench --site site1.local execute fix_car_error
2025-06-27 19:16:13,335 INFO /usr/local/bin/bench --site site1.local console --verbose
2025-06-27 19:17:26,185 INFO /usr/local/bin/bench clear-cache
2025-06-27 19:18:45,485 INFO /usr/local/bin/bench --site site1.local execute customsmart.quick_fix.quick_fix
2025-06-27 19:31:52,702 INFO /usr/local/bin/bench --site site1.local execute customsmart.create_car_doctype.main
2025-06-27 19:32:31,727 INFO /usr/local/bin/bench --site site1.local console
2025-06-27 19:36:45,331 INFO /usr/local/bin/bench --site site1.local --force new-doctype customsmart Car Record
2025-06-27 19:37:17,914 INFO /usr/local/bin/bench frappe --site site1.local make-doctype customsmart Car Record
2025-06-27 20:21:10,089 INFO /usr/local/bin/bench --site site1.local migrate
2025-06-27 20:24:46,316 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-06-27 20:50:53,961 INFO /usr/local/bin/bench --site site1.local migrate
2025-06-27 20:59:43,154 INFO /usr/local/bin/bench --site site1.local execute frappe.client.insert --args @/tmp/workspace_data.json
2025-06-27 21:14:11,121 INFO /usr/local/bin/bench --site site1.local execute frappe.client.insert --args @/tmp/car_sale_invoice_format.json
2025-06-27 21:14:13,270 INFO /usr/local/bin/bench --site site1.local execute frappe.client.save --args @/tmp/car_sale_invoice_format.json
2025-06-28 07:27:02,358 INFO /usr/local/bin/bench start
2025-06-28 07:27:02,731 INFO /usr/local/bin/bench schedule
2025-06-28 07:27:02,749 INFO /usr/local/bin/bench watch
2025-06-28 07:27:02,750 INFO /usr/local/bin/bench worker
2025-06-28 07:27:02,814 INFO /usr/local/bin/bench serve --port 8000
2025-06-28 07:44:32,124 INFO /usr/local/bin/bench --site site1.local list-apps
2025-06-28 07:44:48,866 INFO /usr/local/bin/bench --site site1.local list-doctypes customsmart
2025-06-28 07:45:18,884 INFO /usr/local/bin/bench --site site1.local execute frappe.db.sql('SELECT name FROM tabDocType WHERE name LIKE "%Car%" OR name LIKE "%Record%"')
2025-06-28 07:45:33,078 INFO /usr/local/bin/bench --site site1.local console
2025-06-28 07:45:51,110 INFO /usr/local/bin/bench --site site1.local console
2025-06-28 07:46:07,575 INFO /usr/local/bin/bench --site site1.local console
2025-06-28 07:47:35,505 INFO /usr/local/bin/bench --site site1.local console
2025-06-28 07:47:53,516 INFO /usr/local/bin/bench --site site1.local console
2025-06-28 07:54:26,247 INFO /usr/local/bin/bench get-app customsmart --resolve-deps
2025-06-28 07:59:23,061 INFO /usr/local/bin/bench --site site1.local console
2025-06-28 07:59:42,471 INFO /usr/local/bin/bench --site site1.local export-doc DocType Car Record
2025-06-28 08:03:59,315 INFO /usr/local/bin/bench --site site1.local console
2025-06-28 08:05:57,594 INFO /usr/local/bin/bench --site site1.local export-doc DocType Car Showroom
2025-06-28 08:42:29,419 INFO /usr/local/bin/bench --site site1.local console
2025-06-28 08:42:48,889 INFO /usr/local/bin/bench --site site1.local console
2025-06-28 08:43:08,487 INFO /usr/local/bin/bench --site site1.local console
2025-06-28 08:43:26,512 INFO /usr/local/bin/bench --site site1.local reinstall-app customsmart
2025-06-28 08:43:40,112 INFO /usr/local/bin/bench --site site1.local migrate
2025-06-28 08:47:49,851 INFO /usr/local/bin/bench --site site1.local console
2025-06-28 08:48:20,998 INFO /usr/local/bin/bench --site site1.local reload-doctype Workspace
2025-06-28 08:48:38,085 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-06-28 08:48:57,377 INFO /usr/local/bin/bench --site site1.local build
2025-06-28 08:51:37,966 INFO /usr/local/bin/bench restart
2025-06-28 08:52:21,536 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-06-28 08:52:24,414 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-28 08:52:24,415 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-06-28 08:52:28,598 INFO A newer version of bench is available: 5.22.3 → 5.25.7
2025-06-28 09:46:17,738 INFO /usr/local/bin/bench --site site1.local console
2025-06-28 09:47:39,654 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-06-28 09:48:00,471 INFO /usr/local/bin/bench --site site1.local build
2025-06-28 09:52:18,741 INFO /usr/local/bin/bench --site site1.local console
2025-06-28 09:52:49,759 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-06-28 09:53:19,618 INFO /usr/local/bin/bench --site site1.local build
2025-06-28 09:57:02,976 INFO /usr/local/bin/bench build
2025-06-28 13:46:10,241 INFO /usr/local/bin/bench update
2025-06-28 13:46:22,567 WARNING shallow_clone is set in your bench config.
However without passing the --reset flag, your repositories will be unshallowed.
To avoid this, cancel this operation and run `bench update --reset`.

Consider the consequences of `git reset --hard` on your apps before you run that.
To avoid seeing this warning, set shallow_clone to false in your common_site_config.json
		
2025-06-28 13:48:08,322 WARNING /usr/local/bin/bench update executed with exit code 1
2025-06-28 13:48:10,587 INFO A newer version of bench is available: 5.22.3 → 5.25.7
2025-06-28 13:50:59,155 INFO /usr/local/bin/bench update --pull --reset
2025-06-28 13:51:23,946 LOG pulling frappe
2025-06-28 13:51:23,947 DEBUG cd /home/<USER>/frappe-bench/apps/frappe && git fetch --depth=1 --no-tags upstream version-15
2025-06-28 13:51:26,099 DEBUG cd /home/<USER>/frappe-bench/apps/frappe && git reset --hard upstream/version-15
2025-06-28 13:51:26,839 DEBUG cd /home/<USER>/frappe-bench/apps/frappe && git reflog expire --all
2025-06-28 13:51:26,919 DEBUG cd /home/<USER>/frappe-bench/apps/frappe && git gc --prune=all
2025-06-28 13:51:43,438 DEBUG cd /home/<USER>/frappe-bench/apps/frappe && find . -name "*.pyc" -delete
2025-06-28 13:52:07,747 LOG pulling webshop
2025-06-28 13:52:07,761 DEBUG cd /home/<USER>/frappe-bench/apps/webshop && git fetch --depth=1 --no-tags upstream develop
2025-06-28 13:52:13,769 DEBUG cd /home/<USER>/frappe-bench/apps/webshop && git reset --hard upstream/develop
2025-06-28 13:52:14,983 DEBUG cd /home/<USER>/frappe-bench/apps/webshop && git reflog expire --all
2025-06-28 13:52:15,124 DEBUG cd /home/<USER>/frappe-bench/apps/webshop && git gc --prune=all
2025-06-28 13:52:16,185 DEBUG cd /home/<USER>/frappe-bench/apps/webshop && find . -name "*.pyc" -delete
2025-06-28 13:52:16,473 LOG pulling custom_erp
2025-06-28 13:52:16,474 DEBUG cd /home/<USER>/frappe-bench/apps/custom_erp && git fetch --depth=1 --no-tags origin main
2025-06-28 13:52:22,200 WARNING cd /home/<USER>/frappe-bench/apps/custom_erp && git fetch --depth=1 --no-tags origin main executed with exit code 128
2025-06-28 13:52:22,202 WARNING /usr/local/bin/bench update --pull --reset executed with exit code 1
2025-06-28 13:52:25,968 INFO A newer version of bench is available: 5.22.3 → 5.25.7
2025-06-28 15:06:19,418 INFO /usr/local/bin/bench --site all list-apps
2025-06-28 15:06:48,381 INFO /usr/local/bin/bench list-apps
2025-06-28 15:07:07,296 INFO /usr/local/bin/bench --site all list-apps
2025-06-28 15:07:23,347 INFO /usr/local/bin/bench --site site1.local execute frappe.desk.desktop.get_desktop_page --args customsmart
2025-06-28 15:07:40,583 INFO /usr/local/bin/bench --site site1.local console
2025-06-28 15:12:16,699 INFO /usr/local/bin/bench --site site1.local execute frappe.get_doc --args ["Workspace", "Car Showroom"]
2025-06-28 15:14:57,500 INFO /usr/local/bin/bench --site site1.local execute frappe.get_all --args ["Workspace", {"filters": {"module": "customsmart"}}, ["name", "label", "icon", "public", "is_hidden"]]
2025-06-28 15:15:17,675 INFO /usr/local/bin/bench --site site1.local execute frappe.get_all --args ["Workspace"]
2025-06-28 15:15:37,733 INFO /usr/local/bin/bench --site site1.local execute frappe.get_all --args ["Workspace", {"filters": {"name": "Car Showroom"}}, ["name", "label", "icon", "public", "is_hidden", "module", "sequence_id"]]
2025-06-28 15:15:56,710 INFO /usr/local/bin/bench --site site1.local execute frappe.db.sql --args ["SELECT name, label, icon, public, is_hidden, module FROM `tabWorkspace` WHERE name = \"Car Showroom\"", {"as_dict": True}]
2025-06-28 15:16:19,332 INFO /usr/local/bin/bench --site site1.local execute frappe.db.sql("SELECT name, label, icon, public, is_hidden, module FROM `tabWorkspace` WHERE name = \"Car Showroom\"", as_dict=True)
2025-06-28 15:17:16,029 INFO /usr/local/bin/bench --site site1.local migrate
2025-06-28 15:19:44,146 INFO /usr/local/bin/bench --site site1.local execute frappe.get_all --args ["Workspace"]
2025-06-28 15:20:16,423 INFO /usr/local/bin/bench --site site1.local execute frappe.get_all --args ["Workspace"]
2025-06-28 15:20:49,088 INFO /usr/local/bin/bench clear-cache
2025-06-28 15:21:57,153 INFO /usr/local/bin/bench clear-website-cache
2025-06-28 15:22:28,893 INFO /usr/local/bin/bench --site site1.local execute print(frappe.get_all('Workspace', fields=['name', 'label']))
2025-06-28 15:22:46,838 INFO /usr/local/bin/bench --site site1.local console --execute frappe.get_all('Workspace')
2025-06-28 15:25:01,823 INFO /usr/local/bin/bench --site site1.local execute frappe.desk.desktop.get_workspace_sidebar_items
2025-06-28 15:29:13,018 INFO /usr/local/bin/bench --site site1.local execute frappe.init
2025-06-28 15:33:56,281 INFO /usr/local/bin/bench --site site1.local desk
2025-06-28 15:34:51,255 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_value('Workspace', 'Car Showroom', ['name', 'label', 'icon', 'public', 'is_hidden'], as_dict=True)
2025-06-28 15:40:23,494 INFO /usr/local/bin/bench --site site1.local install-app customsmart
2025-06-28 15:45:33,132 INFO /usr/local/bin/bench --site site1.local execute print([app.app_name for app in frappe.get_all('Installed Application', fields=['app_name'])])
2025-06-28 15:45:54,809 INFO /usr/local/bin/bench --site site1.local execute frappe.get_installed_apps
2025-06-28 15:46:16,314 INFO /usr/local/bin/bench --site site1.local execute frappe.get_doc --args ["Workspace", "Car Showroom"]
2025-06-28 15:49:45,200 INFO /usr/local/bin/bench --site site1.local execute frappe.db.exists('Workspace', 'Car Showroom')
2025-06-28 15:55:10,094 INFO /usr/local/bin/bench --site site1.local run install_workspace.py
2025-06-28 15:56:06,583 INFO /usr/local/bin/bench --site site1.local run-python install_workspace.py
2025-06-28 15:56:33,166 INFO /usr/local/bin/bench --site site1.local console
2025-06-28 15:58:19,371 INFO /usr/local/bin/bench --site site1.local execute exec(open('install_workspace_simple.py').read())
2025-06-28 15:58:56,263 INFO /usr/local/bin/bench --site site1.local execute 
import frappe
import json

workspace_file = '/home/<USER>/frappe-bench/apps/customsmart/customsmart/customsmart/workspace/car_showroom/car_showroom.json'

with open(workspace_file, 'r', encoding='utf-8') as f:
    workspace_data = json.load(f)

if frappe.db.exists('Workspace', 'Car Showroom'):
    print('Workspace موجود')
    doc = frappe.get_doc('Workspace', 'Car Showroom')
    print('تم العثور على workspace:', doc.name)
else:
    print('Workspace غير موجود - سيتم الإنشاء')
    doc = frappe.new_doc('Workspace')
    doc.name = workspace_data.get('name')
    doc.label = workspace_data.get('label')
    doc.icon = workspace_data.get('icon')
    doc.module = workspace_data.get('module')
    doc.public = workspace_data.get('public', 1)
    doc.is_hidden = workspace_data.get('is_hidden', 0)
    doc.insert()
    frappe.db.commit()
    print('تم إنشاء workspace بنجاح')

2025-06-28 16:03:03,051 INFO /usr/local/bin/bench --site site1.local import-fixtures
2025-06-28 16:16:08,703 INFO /usr/local/bin/bench --site site1.local import-doc customsmart Workspace /home/<USER>/frappe-bench/apps/customsmart/customsmart/customsmart/workspace/car_showroom/car_showroom.json
2025-06-28 16:17:08,959 INFO /usr/local/bin/bench --site site1.local import-doc /home/<USER>/frappe-bench/apps/customsmart/customsmart/customsmart/workspace/car_showroom/car_showroom.json
2025-06-28 16:19:00,793 INFO /usr/local/bin/bench --site site1.local mariadb
2025-06-28 16:22:21,994 INFO /usr/local/bin/bench --site site1.local execute print('Workspaces in database:')
2025-06-28 16:23:10,487 INFO /usr/local/bin/bench version
2025-06-28 16:23:12,840 INFO /usr/local/bin/bench list-sites
2025-06-28 16:23:13,769 INFO /usr/local/bin/bench list-apps
2025-06-28 16:23:31,438 INFO /usr/local/bin/bench --site site1.local console
2025-06-28 16:23:55,322 INFO /usr/local/bin/bench --site site1.local console
2025-06-28 16:24:15,748 INFO /usr/local/bin/bench restart
2025-06-28 16:26:45,157 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-06-28 16:26:47,101 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-28 16:26:47,102 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-06-28 16:26:48,755 INFO A newer version of bench is available: 5.22.3 → 5.25.7
2025-06-28 16:29:14,171 INFO /usr/local/bin/bench start
2025-06-28 16:47:03,101 INFO /usr/local/bin/bench --site site1.local execute customsmart.install_car_showroom.install_car_showroom_customizations
2025-06-28 16:56:36,467 INFO /usr/local/bin/bench --site newsmart.local install-app customsmart
2025-06-28 16:57:35,061 INFO /usr/local/bin/bench start
2025-06-28 16:57:35,541 INFO /usr/local/bin/bench worker
2025-06-28 16:57:35,555 INFO /usr/local/bin/bench schedule
2025-06-28 16:57:35,563 INFO /usr/local/bin/bench watch
2025-06-28 16:57:35,589 INFO /usr/local/bin/bench serve --port 8000
2025-06-28 16:59:47,532 INFO /usr/local/bin/bench migrate
2025-06-28 18:00:01,454 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-30 09:14:04,009 INFO /usr/local/bin/bench start
2025-06-30 09:14:04,457 INFO /usr/local/bin/bench schedule
2025-06-30 09:14:04,471 INFO /usr/local/bin/bench serve --port 8000
2025-06-30 09:14:04,529 INFO /usr/local/bin/bench worker
2025-06-30 09:14:04,623 INFO /usr/local/bin/bench watch
2025-06-30 09:34:08,367 INFO /usr/local/bin/bench --site site1.local execute customsmart.install_qr_system.install_qr_system
2025-06-30 09:35:44,503 INFO /usr/local/bin/bench --site site1.local execute customsmart.install_qr_system.install_qr_system
2025-06-30 09:40:51,899 INFO /usr/local/bin/bench update
2025-06-30 09:40:54,230 WARNING shallow_clone is set in your bench config.
However without passing the --reset flag, your repositories will be unshallowed.
To avoid this, cancel this operation and run `bench update --reset`.

Consider the consequences of `git reset --hard` on your apps before you run that.
To avoid seeing this warning, set shallow_clone to false in your common_site_config.json
		
2025-06-30 09:41:34,592 WARNING /usr/local/bin/bench update executed with exit code 1
2025-06-30 09:41:35,569 INFO A newer version of bench is available: 5.22.3 → 5.25.7
2025-06-30 09:43:11,620 INFO /usr/local/bin/bench build
2025-06-30 09:45:49,581 INFO /usr/local/bin/bench --site site1.local uninstall erpnext
2025-06-30 09:47:37,381 INFO /usr/local/bin/bench --site site1.local install-app frappe
2025-06-30 09:48:02,725 INFO /usr/local/bin/bench --site site1.local uninstall-app frappe
2025-06-30 09:50:12,161 INFO /usr/local/bin/bench --site site1.local uninstall-app erpnext
2025-06-30 09:52:10,069 INFO /usr/local/bin/bench --site site1.local uninstall-app customsmart
2025-06-30 09:53:07,011 INFO /usr/local/bin/bench build
2025-06-30 09:55:54,632 INFO /usr/local/bin/bench --site site1.local install-app erpnext
2025-06-30 09:55:58,237 INFO /usr/local/bin/bench --site site1.local install-app customsmart
2025-06-30 09:57:27,028 INFO /usr/local/bin/bench --site site1.local reinstall-app erpnext
2025-06-30 09:57:46,715 INFO /usr/local/bin/bench --site site1.local reinstall erpnext
2025-06-30 09:58:00,521 INFO /usr/local/bin/bench --site site1.local reinstall
2025-06-30 10:02:55,350 INFO /usr/local/bin/bench build
2025-06-30 10:04:05,203 INFO /usr/local/bin/bench migrate
2025-06-30 10:06:18,890 INFO /usr/local/bin/bench start
2025-06-30 10:06:19,803 INFO /usr/local/bin/bench serve --port 8000
2025-06-30 10:06:19,847 INFO /usr/local/bin/bench schedule
2025-06-30 10:06:19,854 INFO /usr/local/bin/bench worker
2025-06-30 10:06:19,930 INFO /usr/local/bin/bench watch
2025-06-30 10:07:26,274 INFO /usr/local/bin/bench migrate
2025-06-30 10:07:48,950 INFO /usr/local/bin/bench --site site1.local install erpnext
2025-06-30 10:07:48,966 WARNING /usr/local/bin/bench --site site1.local install erpnext executed with exit code 2
2025-06-30 10:07:50,061 INFO A newer version of bench is available: 5.22.3 → 5.25.7
2025-06-30 10:08:04,631 INFO /usr/local/bin/bench --site site1.local instal-appl erpnext
2025-06-30 10:08:25,062 INFO /usr/local/bin/bench --site site1.local install-app erpnext
2025-06-30 10:12:07,868 INFO /usr/local/bin/bench build
2025-06-30 10:16:04,805 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-06-30 10:18:33,868 INFO /usr/local/bin/bench build
2025-06-30 10:19:48,343 INFO /usr/local/bin/bench start
2025-06-30 10:19:50,209 INFO /usr/local/bin/bench worker
2025-06-30 10:19:50,253 INFO /usr/local/bin/bench schedule
2025-06-30 10:19:50,268 INFO /usr/local/bin/bench serve --port 8000
2025-06-30 10:19:50,328 INFO /usr/local/bin/bench watch
2025-06-30 10:36:20,209 INFO /usr/local/bin/bench --site site1.local execute customsmart.install_car_showroom.install_car_showroom_customizations
2025-06-30 10:37:36,559 INFO /usr/local/bin/bench --site site1.local install-app customsmart
2025-06-30 10:42:06,850 INFO /usr/local/bin/bench --site site1.local execute customsmart.install_car_showroom.install_car_showroom_customizations
2025-06-30 10:46:25,489 INFO /usr/local/bin/bench restart
2025-06-30 10:46:36,441 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-06-30 10:46:37,147 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-30 10:46:37,147 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-06-30 10:46:38,409 INFO A newer version of bench is available: 5.22.3 → 5.25.7
2025-06-30 10:47:01,863 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-06-30 10:47:41,261 INFO /usr/local/bin/bench --site site1.local migrate
2025-06-30 10:49:50,373 INFO /usr/local/bin/bench --site site1.local reload-doc frappe core workspace
2025-06-30 10:50:18,260 INFO /usr/local/bin/bench build --app customsmart
2025-06-30 10:51:20,656 INFO /usr/local/bin/bench --site site1.local execute customsmart.restore_workspaces.complete_workspace_fix
2025-06-30 10:51:48,017 INFO /usr/local/bin/bench --site site1.local mariadb
2025-06-30 10:57:16,375 INFO /usr/local/bin/bench --site site1.local console
2025-06-30 10:57:56,365 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-06-30 10:57:59,532 INFO /usr/local/bin/bench --site site1.local clear-website-cache
2025-06-30 11:00:35,174 INFO /usr/local/bin/bench --site site1.local execute 
import frappe

# تحديث Workspaces لتكون عامة
frappe.db.sql('''UPDATE `tabWorkspace` SET public = 1, for_user = \'\', parent_page = \'\' WHERE name IN (\'Home\', \'Accounting\', \'Selling\', \'Buying\', \'Stock\', \'Assets\', \'Projects\', \'CRM\', \'Support\', \'HR\', \'Manufacturing\', \'Website\', \'Tools\', \'Settings\')''')

# إعادة ترتيب
workspace_order = [(\'Home\', 1), (\'Accounting\', 2), (\'Selling\', 3), (\'Buying\', 4), (\'Stock\', 5), (\'Assets\', 6), (\'Projects\', 7), (\'CRM\', 8), (\'Support\', 9), (\'HR\', 10), (\'Manufacturing\', 11), (\'Website\', 12), (\'Tools\', 13), (\'Settings\', 14)]

for workspace, sequence in workspace_order:
    if frappe.db.exists('Workspace', workspace):
        frappe.db.sql('UPDATE `tabWorkspace` SET sequence_id = %s WHERE name = %s', (sequence, workspace))

# تحديث إعدادات إضافية
frappe.db.sql('''UPDATE `tabWorkspace` SET restrict_to_domain = \'\', is_standard = 1 WHERE name IN (\'Home\', \'Accounting\', \'Selling\', \'Buying\', \'Stock\', \'Assets\', \'Projects\', \'CRM\', \'Support\', \'HR\', \'Manufacturing\', \'Website\', \'Tools\', \'Settings\')''')

# commit
frappe.db.commit()

# مسح cache
frappe.clear_cache()

print('تم إصلاح مشكلة Workspaces بنجاح!')

2025-06-30 11:01:15,333 INFO /usr/local/bin/bench --site site1.local execute customsmart.fix_workspace.fix_workspace_visibility
2025-06-30 12:26:47,864 INFO /usr/local/bin/bench start
2025-06-30 12:26:48,326 INFO /usr/local/bin/bench watch
2025-06-30 12:26:48,347 INFO /usr/local/bin/bench schedule
2025-06-30 12:26:48,364 INFO /usr/local/bin/bench serve --port 8000
2025-06-30 12:26:48,518 INFO /usr/local/bin/bench worker
2025-06-30 12:42:41,865 INFO /usr/local/bin/bench --site site1.local execute customsmart.workspace_fix.safe_workspace_fix
2025-06-30 12:43:16,930 INFO /usr/local/bin/bench --site site1.local execute customsmart.workspace_fix.check_workspace_status
2025-06-30 12:43:36,766 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-06-30 12:43:39,125 INFO /usr/local/bin/bench --site site1.local clear-website-cache
2025-06-30 12:43:58,308 INFO /usr/local/bin/bench build --app customsmart
2025-06-30 12:45:46,533 INFO /usr/local/bin/bench --site site1.local execute customsmart.quick_setup.setup_customsmart_safely
2025-06-30 12:46:33,677 INFO /usr/local/bin/bench --site site1.local execute customsmart.quick_setup.check_system_health
2025-06-30 12:46:44,105 INFO /usr/local/bin/bench --site site1.local execute customsmart.install_car_showroom.install_car_showroom_customizations
2025-06-30 12:49:50,753 INFO /usr/local/bin/bench new-app printe_custom
2025-06-30 12:49:50,785 LOG creating new app printe_custom
2025-06-30 12:51:03,802 LOG Installing printe_custom
2025-06-30 12:51:04,269 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/printe_custom 
2025-06-30 12:51:14,816 DEBUG bench build --app printe_custom
2025-06-30 12:51:15,679 INFO /usr/local/bin/bench build --app printe_custom
2025-06-30 12:51:40,717 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-06-30 12:51:43,077 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-06-30 12:51:43,077 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-06-30 12:51:45,597 INFO A newer version of bench is available: 5.22.3 → 5.25.7
2025-06-30 12:52:07,128 INFO /usr/local/bin/bench --site site1.local install-app printe_custom
2025-06-30 12:56:29,021 INFO /usr/local/bin/bench --site site1.local execute customsmart.create_tax_invoice_format.install_tax_invoice_system
2025-06-30 12:58:06,324 INFO /usr/local/bin/bench --site site1.local execute customsmart.create_tax_invoice_format.create_tax_invoice_print_format
2025-06-30 12:58:32,216 INFO /usr/local/bin/bench --site site1.local execute 
import frappe
print('Print Formats للـ Sales Invoice:')
formats = frappe.get_all('Print Format', filters={'doc_type': 'Sales Invoice'}, fields=['name', 'disabled'])
for fmt in formats:
    status = 'مفعل' if not fmt.disabled else 'معطل'
    print(f'- {fmt.name}: {status}')

2025-06-30 12:59:05,840 INFO /usr/local/bin/bench --site site1.local execute customsmart.create_tax_invoice_format.check_print_formats
2025-06-30 13:00:33,344 INFO /usr/local/bin/bench --site site1.local execute customsmart.create_tax_invoice_client_script.install_complete_tax_invoice_system
2025-06-30 13:00:59,443 INFO /usr/local/bin/bench --site site1.local execute customsmart.create_tax_invoice_client_script.create_tax_invoice_client_script
2025-06-30 13:01:26,776 INFO /usr/local/bin/bench build --app customsmart
2025-06-30 13:01:58,010 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-06-30 13:07:09,131 INFO /usr/local/bin/bench --site site1.local execute printe_custom.tax_invoice.install_print_custom_system
2025-06-30 13:07:42,944 INFO /usr/local/bin/bench build --app printe_custom
2025-06-30 13:08:07,096 INFO /usr/local/bin/bench --site site1.local execute printe_custom.tax_invoice.check_print_custom_status
2025-06-30 13:09:25,274 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-06-30 13:20:14,723 INFO /usr/local/bin/bench start
2025-06-30 13:20:15,385 INFO /usr/local/bin/bench schedule
2025-06-30 13:20:15,424 INFO /usr/local/bin/bench watch
2025-06-30 13:20:15,475 INFO /usr/local/bin/bench serve --port 8000
2025-06-30 13:20:15,607 INFO /usr/local/bin/bench worker
2025-06-30 13:22:40,854 INFO /usr/local/bin/bench --site site1.local execute printe_custom.tax_invoice.create_luxury_speed_tax_invoice
2025-06-30 13:24:29,370 INFO /usr/local/bin/bench build --app printe_custom
2025-06-30 13:25:17,040 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-06-30 13:26:18,462 INFO /usr/local/bin/bench --site site1.local execute printe_custom.tax_invoice.test_qr_generation
2025-06-30 13:27:31,752 INFO /usr/local/bin/bench --site site1.local execute printe_custom.tax_invoice.test_qr_generation
2025-06-30 13:31:57,831 INFO /usr/local/bin/bench --site site1.local execute printe_custom.smart_installer.smart_install_tax_invoice_system
2025-06-30 13:34:06,192 INFO /usr/local/bin/bench --site site1.local execute printe_custom.file_updater.run_smart_file_updates
2025-06-30 13:34:45,079 INFO /usr/local/bin/bench build --app printe_custom
2025-06-30 13:35:39,379 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-06-30 13:37:37,030 INFO /usr/local/bin/bench --site site1.local execute printe_custom.tax_invoice.test_qr_generation
2025-06-30 13:39:06,539 INFO /usr/local/bin/bench --site site1.local execute printe_custom.smart_installer.smart_install_tax_invoice_system
2025-06-30 13:40:17,362 INFO /usr/local/bin/bench --site site1.local execute printe_custom.tax_invoice.test_qr_generation
2025-06-30 13:43:47,062 INFO /usr/local/bin/bench --site site1.local execute printe_custom.tax_invoice.test_qr_generation
2025-06-30 13:44:18,258 INFO /usr/local/bin/bench --site site1.local execute printe_custom.tax_invoice.install_smart_qr_system
2025-06-30 13:45:15,114 INFO /usr/local/bin/bench build --app printe_custom
2025-06-30 13:45:32,245 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-06-30 13:56:46,893 INFO /usr/local/bin/bench start
2025-06-30 13:56:47,715 INFO /usr/local/bin/bench serve --port 8000
2025-06-30 13:56:47,738 INFO /usr/local/bin/bench schedule
2025-06-30 13:56:47,802 INFO /usr/local/bin/bench watch
2025-06-30 13:56:47,881 INFO /usr/local/bin/bench worker
2025-06-30 13:59:09,993 INFO /usr/local/bin/bench --site site1.local uninstall-app interface_customization
2025-06-30 14:00:02,368 INFO /usr/local/bin/bench build
2025-06-30 14:04:28,084 INFO /usr/local/bin/bench start
2025-06-30 14:04:30,145 INFO /usr/local/bin/bench worker
2025-06-30 14:04:30,159 INFO /usr/local/bin/bench serve --port 8000
2025-06-30 14:04:30,177 INFO /usr/local/bin/bench schedule
2025-06-30 14:04:30,187 INFO /usr/local/bin/bench watch
2025-06-30 14:07:35,819 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-06-30 14:26:38,478 INFO /usr/local/bin/bench --site site1.local execute printe_custom.tax_invoice.smart_generate_qr_for_any_invoice --args {'name': 'TEST-001', 'customer_name': 'عميل تجريبي', 'grand_total': 1000, 'total_taxes_and_charges': 150, 'docstatus': 0} false
2025-06-30 14:28:32,724 INFO /usr/local/bin/bench --site site1.local execute printe_custom.system_test.run_full_system_test
2025-06-30 14:30:11,385 INFO /usr/local/bin/bench --site site1.local --verbose execute 
import frappe
print('🔍 فحص Print Format...')
if frappe.db.exists('Print Format', 'فاتورة ضريبية - السرعة الفاخرة'):
    pf = frappe.get_doc('Print Format', 'فاتورة ضريبية - السرعة الفاخرة')
    print('✅ Print Format موجود')
    print(f'📄 DocType: {pf.doc_type}')
    print(f'🔧 النوع: {pf.print_format_type}')
    if 'generateSmartQRForPrint' in pf.html:
        print('✅ JavaScript للـ QR موجود')
    else:
        print('❌ JavaScript للـ QR غير موجود')
else:
    print('❌ Print Format غير موجود')

2025-06-30 14:30:42,229 INFO /usr/local/bin/bench --site site1.local mariadb -e SELECT name, doc_type, print_format_type FROM tabPrintFormat WHERE name LIKE '%ضريبية%' OR name LIKE '%السرعة%';
2025-06-30 14:31:12,121 INFO /usr/local/bin/bench --site site1.local mariadb
2025-06-30 14:38:09,499 INFO /usr/local/bin/bench --site site1.local execute printe_custom.quick_test.run_complete_test
2025-06-30 15:12:36,487 INFO /usr/local/bin/bench build --app printe_custom
2025-06-30 15:13:24,657 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-06-30 15:13:56,902 INFO /usr/local/bin/bench --site site1.local execute printe_custom.tax_invoice.create_luxury_speed_tax_invoice
2025-06-30 16:42:58,954 INFO /usr/local/bin/bench update
2025-06-30 16:43:07,754 WARNING shallow_clone is set in your bench config.
However without passing the --reset flag, your repositories will be unshallowed.
To avoid this, cancel this operation and run `bench update --reset`.

Consider the consequences of `git reset --hard` on your apps before you run that.
To avoid seeing this warning, set shallow_clone to false in your common_site_config.json
		
2025-06-30 16:44:39,935 WARNING /usr/local/bin/bench update executed with exit code 1
2025-06-30 16:44:41,753 INFO A newer version of bench is available: 5.22.3 → 5.25.7
2025-06-30 16:45:02,730 INFO /usr/local/bin/bench build
2025-06-30 16:48:01,593 INFO /usr/local/bin/bench --site site1.local install-app erpsmart_car
2025-06-30 16:48:49,110 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-06-30 16:48:53,668 INFO /usr/local/bin/bench start
2025-06-30 16:48:55,208 INFO /usr/local/bin/bench serve --port 8000
2025-06-30 16:48:55,223 INFO /usr/local/bin/bench schedule
2025-06-30 16:48:55,354 INFO /usr/local/bin/bench worker
2025-06-30 16:48:55,355 INFO /usr/local/bin/bench watch
2025-06-30 17:02:31,296 INFO /usr/local/bin/bench --site site1.local install-app custom_erp
2025-07-01 13:30:49,927 INFO /usr/local/bin/bench start
2025-07-01 13:30:50,734 INFO /usr/local/bin/bench schedule
2025-07-01 13:30:50,752 INFO /usr/local/bin/bench watch
2025-07-01 13:30:50,754 INFO /usr/local/bin/bench serve --port 8000
2025-07-01 13:30:50,828 INFO /usr/local/bin/bench worker
2025-07-01 14:11:46,356 INFO /usr/local/bin/bench --site site1.local uninstall-app custom_erp
2025-07-01 14:12:49,429 INFO /usr/local/bin/bench --site site1.local uninstall-app erpsmart_car
2025-07-01 14:13:11,888 INFO /usr/local/bin/bench --site site1.local uninstall-app customsmart
2025-07-01 14:13:41,126 INFO /usr/local/bin/bench --site site1.local uninstall-app whatsapp
2025-07-01 14:14:58,244 INFO /usr/local/bin/bench --site site1.local install-app erp_optimizer
2025-07-01 14:16:28,897 INFO /usr/local/bin/bench --site site1.local install-app erp_optimizer
2025-07-01 14:26:20,251 INFO /usr/local/bin/bench restart
2025-07-01 14:26:44,271 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-07-01 14:26:44,867 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-07-01 14:26:44,867 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-07-01 14:26:45,947 INFO A newer version of bench is available: 5.22.3 → 5.25.8
2025-07-01 14:27:02,204 INFO /usr/local/bin/bench clear-cache
2025-07-01 14:27:16,948 INFO /usr/local/bin/bench --site site1.local install-app erp_optimizer
2025-07-01 14:27:35,403 INFO /usr/local/bin/bench --site site1.local list-apps
2025-07-01 14:30:02,882 INFO /usr/local/bin/bench clear-cache
2025-07-01 14:30:06,639 INFO /usr/local/bin/bench --site site1.local install-app erp_optimizer
2025-07-01 14:30:24,688 INFO /usr/local/bin/bench --site site1.local install-app erp_optimizer --verbose
2025-07-01 14:30:39,167 INFO /usr/local/bin/bench --site site1.local install-app erp_optimizer
2025-07-01 14:30:55,709 INFO /usr/local/bin/bench list-apps
2025-07-01 14:34:29,613 INFO /usr/local/bin/bench clear-cache
2025-07-01 14:34:51,706 INFO /usr/local/bin/bench list-apps
2025-07-01 14:35:29,571 INFO /usr/local/bin/bench list-apps
2025-07-01 14:36:27,170 INFO /usr/local/bin/bench --site site1.local install-app erp_optimizer
2025-07-01 14:36:57,867 INFO /usr/local/bin/bench get-app --resolve-deps apps/erp_optimizer
2025-07-01 14:36:57,896 WARNING /usr/local/bin/bench get-app --resolve-deps apps/erp_optimizer executed with exit code 1
2025-07-01 14:36:59,030 INFO A newer version of bench is available: 5.22.3 → 5.25.8
2025-07-01 14:39:37,965 INFO /usr/local/bin/bench --site site1.local console
2025-07-01 14:41:42,220 INFO /usr/local/bin/bench new-app erp_optimizer_ui
2025-07-01 14:41:42,227 LOG creating new app erp_optimizer_ui
2025-07-01 14:42:47,674 LOG Installing erp_optimizer_ui
2025-07-01 14:42:47,697 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/erp_optimizer_ui 
2025-07-01 14:42:55,178 DEBUG bench build --app erp_optimizer_ui
2025-07-01 14:42:55,582 INFO /usr/local/bin/bench build --app erp_optimizer_ui
2025-07-01 14:43:22,466 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-07-01 14:43:23,058 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-07-01 14:43:23,058 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-07-01 14:43:23,719 INFO A newer version of bench is available: 5.22.3 → 5.25.8
2025-07-01 14:46:30,386 INFO /usr/local/bin/bench start
2025-07-01 14:46:30,738 INFO /usr/local/bin/bench serve --port 8000
2025-07-01 14:46:30,763 INFO /usr/local/bin/bench schedule
2025-07-01 14:46:30,773 INFO /usr/local/bin/bench worker
2025-07-01 14:46:30,785 INFO /usr/local/bin/bench watch
2025-07-01 14:51:57,884 INFO /usr/local/bin/bench clear-cache
2025-07-01 14:52:18,400 INFO /usr/local/bin/bench list-apps
2025-07-01 14:52:53,207 INFO /usr/local/bin/bench --site site1.local install-app erp_optimizer_ui
2025-07-01 14:56:26,617 INFO /usr/local/bin/bench clear-cache
2025-07-01 14:56:29,300 INFO /usr/local/bin/bench list-apps
2025-07-01 14:56:59,863 INFO /usr/local/bin/bench list-apps
2025-07-01 14:57:19,854 INFO /usr/local/bin/bench restart
2025-07-01 14:57:25,396 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-07-01 14:57:25,830 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-07-01 14:57:25,831 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-07-01 14:57:26,856 INFO A newer version of bench is available: 5.22.3 → 5.25.8
2025-07-01 14:57:44,869 INFO /usr/local/bin/bench list-apps
2025-07-01 14:58:09,148 INFO /usr/local/bin/bench --site site1.local console
2025-07-01 14:58:40,017 INFO /usr/local/bin/bench --site site1.local uninstall-app whatsapp --yes
2025-07-01 14:59:17,838 INFO /usr/local/bin/bench --site site1.local install-app erp_optimizer_ui
2025-07-01 15:00:11,525 INFO /usr/local/bin/bench --site site1.local list-apps
2025-07-01 15:00:35,510 INFO /usr/local/bin/bench migrate
2025-07-01 15:01:08,551 INFO /usr/local/bin/bench build
2025-07-01 15:06:03,931 INFO /usr/local/bin/bench start
2025-07-01 15:06:04,684 INFO /usr/local/bin/bench worker
2025-07-01 15:06:04,720 INFO /usr/local/bin/bench serve --port 8000
2025-07-01 15:06:04,720 INFO /usr/local/bin/bench schedule
2025-07-01 15:06:04,808 INFO /usr/local/bin/bench watch
2025-07-01 15:11:26,708 INFO /usr/local/bin/bench --site site1.local list-apps --format table
2025-07-01 15:11:48,595 INFO /usr/local/bin/bench --site site1.local list-apps
2025-07-01 15:12:24,196 INFO /usr/local/bin/bench --site site1.local console
2025-07-01 15:13:13,580 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.fixtures.install
2025-07-01 15:14:01,128 INFO /usr/local/bin/bench build --app erp_optimizer_ui
2025-07-01 15:14:42,972 INFO /usr/local/bin/bench restart
2025-07-01 15:14:49,044 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-07-01 15:14:49,626 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-07-01 15:14:49,627 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-07-01 15:14:52,153 INFO A newer version of bench is available: 5.22.3 → 5.25.8
2025-07-01 15:21:58,928 INFO /usr/local/bin/bench build
2025-07-01 15:23:40,278 INFO /usr/local/bin/bench start
2025-07-01 15:23:42,046 INFO /usr/local/bin/bench watch
2025-07-01 15:23:42,113 INFO /usr/local/bin/bench schedule
2025-07-01 15:23:42,133 INFO /usr/local/bin/bench worker
2025-07-01 15:23:42,229 INFO /usr/local/bin/bench serve --port 8000
2025-07-01 15:40:43,600 INFO /usr/local/bin/bench --site site1.local import-doc /home/<USER>/frappe-bench/apps/erp_optimizer_ui/erp_optimizer_ui/fixtures/custom_field/sales_invoice_custom_fields.json
2025-07-01 15:41:29,797 INFO /usr/local/bin/bench --site site1.local console
2025-07-01 15:42:36,665 INFO /usr/local/bin/bench --site site1.local console
2025-07-01 15:43:14,866 INFO /usr/local/bin/bench --site site1.local console
2025-07-01 15:43:46,713 INFO /usr/local/bin/bench --site site1.local import-doc /home/<USER>/frappe-bench/apps/erp_optimizer_ui/erp_optimizer_ui/workspace/المبيعات.json
2025-07-01 15:44:16,252 INFO /usr/local/bin/bench --site site1.local console
2025-07-01 15:44:49,799 INFO /usr/local/bin/bench build --app erp_optimizer_ui
2025-07-01 15:45:26,640 INFO /usr/local/bin/bench clear-cache
2025-07-01 15:45:29,098 INFO /usr/local/bin/bench clear-website-cache
2025-07-01 15:46:12,782 INFO /usr/local/bin/bench build --app erp_optimizer_ui
2025-07-01 15:46:55,271 INFO /usr/local/bin/bench clear-cache
2025-07-01 15:47:31,436 INFO /usr/local/bin/bench restart
2025-07-01 15:47:38,308 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-07-01 15:47:39,178 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-07-01 15:47:39,178 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-07-01 15:47:40,475 INFO A newer version of bench is available: 5.22.3 → 5.25.8
2025-07-01 15:48:02,951 INFO /usr/local/bin/bench --site site1.local console
2025-07-01 15:49:00,742 INFO /usr/local/bin/bench clear-cache
2025-07-02 12:57:07,846 INFO /usr/local/bin/bench start
2025-07-02 12:57:09,305 INFO /usr/local/bin/bench watch
2025-07-02 12:57:09,381 INFO /usr/local/bin/bench schedule
2025-07-02 12:57:09,395 INFO /usr/local/bin/bench worker
2025-07-02 12:57:09,528 INFO /usr/local/bin/bench serve --port 8000
2025-07-02 13:11:20,029 INFO /usr/local/bin/bench --site newsmart.local list-apps
2025-07-02 13:11:43,883 INFO /usr/local/bin/bench --site site1.local list-apps
2025-07-02 13:13:50,718 INFO /usr/local/bin/bench --site site1.local migrate
2025-07-02 13:15:09,125 INFO /usr/local/bin/bench --site site1.local console
2025-07-02 13:18:49,851 INFO /usr/local/bin/bench --site site1.local execute frappe.get_all('Workspace', filters={'module': 'Selling'}, fields=['name', 'label', 'is_standard'])
2025-07-02 13:20:34,221 INFO /usr/local/bin/bench --site site1.local export-fixtures erp_optimizer_ui
2025-07-02 13:20:49,672 INFO /usr/local/bin/bench --site site1.local export-fixtures
2025-07-02 13:21:05,336 INFO /usr/local/bin/bench --site site1.local migrate
2025-07-02 13:22:45,021 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-02 13:23:16,618 INFO /usr/local/bin/bench --site site1.local enable-app erp_optimizer_ui
2025-07-02 13:23:33,976 INFO /usr/local/bin/bench --site site1.local install-app erp_optimizer_ui
2025-07-02 13:24:10,582 INFO /usr/local/bin/bench --site site1.local show-errors
2025-07-02 13:24:56,416 INFO /usr/local/bin/bench --site site1.local execute frappe.core.doctype.data_import.data_import.import_file_by_path('/home/<USER>/frappe-bench/apps/erp_optimizer_ui/erp_optimizer_ui/workspace/المبيعات.json', 'Administrator')
2025-07-02 13:25:21,897 INFO /usr/local/bin/bench --site site1.local set-config maintenance_mode 0
2025-07-02 13:25:40,748 INFO /usr/local/bin/bench restart
2025-07-02 13:25:56,680 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-07-02 13:25:57,690 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-07-02 13:25:57,691 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-07-02 13:25:59,942 INFO A newer version of bench is available: 5.22.3 → 5.25.9
2025-07-02 13:26:21,069 INFO /usr/local/bin/bench build
2025-07-02 13:29:23,412 INFO /usr/local/bin/bench --site site1.local execute frappe.get_doc('Workspace', 'المبيعات').save()
2025-07-02 13:30:00,458 INFO /usr/local/bin/bench --site site1.local execute frappe.get_doc('Workspace', 'Selling').save()
2025-07-02 13:30:47,243 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_all('Workspace', fields=['name', 'module'])
2025-07-02 13:31:07,048 INFO /usr/local/bin/bench --site site1.local execute print(frappe.db.sql('SELECT name, module FROM tabWorkspace'))
2025-07-02 13:31:59,108 INFO /usr/local/bin/bench build
2025-07-02 13:35:07,785 INFO /usr/local/bin/bench --site site1.local migrate
2025-07-02 13:38:32,514 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-02 13:38:49,663 INFO /usr/local/bin/bench --site site1.local set-config maintenance_mode 0
2025-07-02 13:50:01,100 INFO /usr/local/bin/bench --site site1.local uninstall-app erp_optimizer_ui
2025-07-02 13:51:22,014 INFO /usr/local/bin/bench --site site1.local install-app erp_optimizer_ui
2025-07-02 13:52:30,477 INFO /usr/local/bin/bench --site site1.local install-app erp_optimizer_ui
2025-07-02 13:53:01,437 INFO /usr/local/bin/bench --site site1.local execute from erp_optimizer_ui.setup import after_install; after_install()
2025-07-02 13:53:27,826 INFO /usr/local/bin/bench --site site1.local execute import frappe; frappe.get_attr('erp_optimizer_ui.setup.after_install')()
2025-07-02 13:54:09,625 INFO /usr/local/bin/bench --site site1.local execute execfile('install_workspaces.py')
2025-07-02 13:54:33,613 INFO /usr/local/bin/bench --site site1.local execute import os, json; workspace_path = '/home/<USER>/frappe-bench/apps/erp_optimizer_ui/erp_optimizer_ui/workspace'; [print(f) for f in os.listdir(workspace_path) if f.endswith('.json')]
2025-07-02 13:57:45,094 INFO /usr/local/bin/bench --site site1.local uninstall-app erp_optimizer_ui
2025-07-02 13:58:29,093 INFO /usr/local/bin/bench --site site1.local install-app erp_optimizer_ui
2025-07-02 13:59:22,010 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-02 13:59:42,829 INFO /usr/local/bin/bench build
2025-07-02 14:01:50,639 INFO /usr/local/bin/bench start
2025-07-02 14:01:52,770 INFO /usr/local/bin/bench serve --port 8000
2025-07-02 14:01:52,811 INFO /usr/local/bin/bench watch
2025-07-02 14:01:52,890 INFO /usr/local/bin/bench worker
2025-07-02 14:01:52,909 INFO /usr/local/bin/bench schedule
2025-07-02 14:06:07,737 INFO /usr/local/bin/bench build
2025-07-02 14:09:36,371 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-02 14:10:30,136 INFO /usr/local/bin/bench --site site1.local execute from erp_optimizer_ui.config import execute; execute()
2025-07-02 14:11:06,334 INFO /usr/local/bin/bench --site site1.local execute import frappe; frappe.reload_doc('erp_optimizer_ui', 'workspace', 'المبيعات')
2025-07-02 14:11:30,376 INFO /usr/local/bin/bench --site site1.local set-config maintenance_mode 0
2025-07-02 14:11:52,877 INFO /usr/local/bin/bench --site site1.local migrate
2025-07-02 14:15:03,591 INFO /usr/local/bin/bench start
2025-07-02 14:15:04,802 INFO /usr/local/bin/bench serve --port 8000
2025-07-02 14:15:04,853 INFO /usr/local/bin/bench schedule
2025-07-02 14:15:04,881 INFO /usr/local/bin/bench worker
2025-07-02 14:15:04,900 INFO /usr/local/bin/bench watch
2025-07-03 14:33:49,258 INFO /usr/local/bin/bench start
2025-07-03 14:33:49,880 INFO /usr/local/bin/bench watch
2025-07-03 14:33:49,891 INFO /usr/local/bin/bench schedule
2025-07-03 14:33:49,942 INFO /usr/local/bin/bench worker
2025-07-03 14:33:50,082 INFO /usr/local/bin/bench serve --port 8000
2025-07-03 14:36:43,797 INFO /usr/local/bin/bench get-app --branch Version-15 https://github.com/defendicon/POS-Awesome-V15
2025-07-03 14:36:43,919 LOG Getting POS-Awesome-V15
2025-07-03 14:36:43,919 DEBUG cd ./apps && git clone https://github.com/defendicon/POS-Awesome-V15 --branch Version-15 --depth 1 --origin upstream
2025-07-03 14:36:51,020 LOG Installing posawesome
2025-07-03 14:36:51,022 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/posawesome 
2025-07-03 14:37:57,370 DEBUG cd /home/<USER>/frappe-bench/apps/posawesome && yarn install --check-files
2025-07-03 14:42:02,074 WARNING cd /home/<USER>/frappe-bench/apps/posawesome && yarn install --check-files executed with exit code 1
2025-07-03 14:42:02,183 WARNING /usr/local/bin/bench get-app --branch Version-15 https://github.com/defendicon/POS-Awesome-V15 executed with exit code 1
2025-07-03 14:42:05,980 INFO A newer version of bench is available: 5.22.3 → 5.25.9
2025-07-03 15:08:27,108 INFO /usr/local/bin/bench get-app --branch Version-15 https://github.com/defendicon/POS-Awesome-V15
2025-07-03 15:08:27,474 LOG Getting POS-Awesome-V15
2025-07-03 15:08:27,511 DEBUG cd ./apps && git clone https://github.com/defendicon/POS-Awesome-V15 --branch Version-15 --depth 1 --origin upstream
2025-07-03 15:08:37,114 LOG Installing posawesome
2025-07-03 15:08:37,116 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/posawesome 
2025-07-03 15:08:53,128 DEBUG cd /home/<USER>/frappe-bench/apps/posawesome && yarn install --check-files
2025-07-03 15:12:46,030 WARNING cd /home/<USER>/frappe-bench/apps/posawesome && yarn install --check-files executed with exit code 1
2025-07-03 15:12:46,033 WARNING /usr/local/bin/bench get-app --branch Version-15 https://github.com/defendicon/POS-Awesome-V15 executed with exit code 1
2025-07-03 15:12:47,855 INFO A newer version of bench is available: 5.22.3 → 5.25.9
2025-07-03 15:14:45,989 INFO /usr/local/bin/bench build --app posawesome
2025-07-03 15:25:17,451 INFO /usr/local/bin/bench build --app posawesome
2025-07-03 15:26:25,364 INFO /usr/local/bin/bench --site site1.local install-app posawesome
2025-07-03 15:32:21,908 INFO /usr/local/bin/bench --site site1.local install-app posawesome
2025-07-03 15:35:10,679 INFO /usr/local/bin/bench build --app posawesome
2025-07-03 15:37:18,321 INFO /usr/local/bin/bench --site site1.local migrate
2025-07-03 15:37:34,880 INFO /usr/local/bin/bench --site site1.local migrate
2025-07-03 15:37:47,421 INFO /usr/local/bin/bench start
2025-07-03 15:37:48,382 INFO /usr/local/bin/bench worker
2025-07-03 15:37:48,424 INFO /usr/local/bin/bench watch
2025-07-03 15:37:48,428 INFO /usr/local/bin/bench schedule
2025-07-03 15:37:48,586 INFO /usr/local/bin/bench serve --port 8000
2025-07-03 15:37:54,919 INFO /usr/local/bin/bench --site site1.local migrate
2025-07-03 19:45:35,018 INFO /usr/local/bin/bench start
2025-07-03 19:45:36,146 INFO /usr/local/bin/bench watch
2025-07-03 19:45:36,195 INFO /usr/local/bin/bench serve --port 8000
2025-07-03 19:45:36,202 INFO /usr/local/bin/bench schedule
2025-07-03 19:45:36,437 INFO /usr/local/bin/bench worker
2025-07-03 19:58:42,062 INFO /usr/local/bin/bench --site site1.local migrate
2025-07-03 19:59:47,530 INFO /usr/local/bin/bench --site site1.local migrate
2025-07-03 20:01:24,898 INFO /usr/local/bin/bench --site site1.local migrate
2025-07-03 20:08:25,790 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.setup.install_workspaces.run
2025-07-03 20:12:03,633 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.install_workspaces.run
2025-07-03 20:16:21,309 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.install_workspaces.run
2025-07-03 20:16:31,762 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.install_workspaces
2025-07-03 20:16:53,514 INFO /usr/local/bin/bench build
2025-07-03 20:25:17,380 INFO /usr/local/bin/bench --site site1.local migrate
2025-07-03 21:13:48,217 INFO /usr/local/bin/bench --site site1.local migrate
2025-07-03 21:42:21,813 INFO /usr/local/bin/bench export-fixtures --app erp_optimizer_ui
2025-07-03 21:55:59,879 INFO /usr/local/bin/bench --site site1.local migrate
2025-07-03 21:58:37,434 INFO /usr/local/bin/bench --site site1.local migrate
2025-07-07 14:37:46,920 INFO /usr/local/bin/bench start
2025-07-07 14:37:47,581 INFO /usr/local/bin/bench watch
2025-07-07 14:37:47,586 INFO /usr/local/bin/bench schedule
2025-07-07 14:37:47,593 INFO /usr/local/bin/bench serve --port 8000
2025-07-07 14:37:47,679 INFO /usr/local/bin/bench worker
2025-07-07 15:12:17,402 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.install_workspaces.run
2025-07-07 15:12:37,847 INFO /usr/local/bin/bench --site site1.local migrate
2025-07-07 15:28:46,498 INFO /usr/local/bin/bench --site site1.local migrate
2025-07-07 15:31:43,203 INFO /usr/local/bin/bench --site site1.local build
2025-07-07 15:34:16,819 INFO /usr/local/bin/bench --site site1.local migrate
2025-07-07 15:38:13,744 INFO /usr/local/bin/bench start
2025-07-07 15:38:15,378 INFO /usr/local/bin/bench watch
2025-07-07 15:38:15,523 INFO /usr/local/bin/bench worker
2025-07-07 15:38:15,618 INFO /usr/local/bin/bench schedule
2025-07-07 15:38:15,785 INFO /usr/local/bin/bench serve --port 8000
2025-07-11 14:56:17,326 INFO /usr/local/bin/bench start
2025-07-11 14:56:19,328 INFO /usr/local/bin/bench serve --port 8000
2025-07-11 14:56:19,329 INFO /usr/local/bin/bench watch
2025-07-11 14:56:19,456 INFO /usr/local/bin/bench schedule
2025-07-11 14:56:19,478 INFO /usr/local/bin/bench worker
2025-07-13 14:09:39,987 INFO /usr/local/bin/bench start
2025-07-13 14:09:40,638 INFO /usr/local/bin/bench serve --port 8000
2025-07-13 14:09:40,647 INFO /usr/local/bin/bench worker
2025-07-13 14:09:40,713 INFO /usr/local/bin/bench watch
2025-07-13 14:09:40,727 INFO /usr/local/bin/bench schedule
2025-07-13 14:22:56,749 INFO /usr/local/bin/bench start
2025-07-13 14:22:58,460 INFO /usr/local/bin/bench worker
2025-07-13 14:22:58,492 INFO /usr/local/bin/bench watch
2025-07-13 14:22:58,521 INFO /usr/local/bin/bench schedule
2025-07-13 14:22:58,531 INFO /usr/local/bin/bench serve --port 8000
2025-07-13 14:25:15,921 INFO /usr/local/bin/bench start
2025-07-13 14:25:17,517 INFO /usr/local/bin/bench serve --port 8000
2025-07-13 14:25:17,653 INFO /usr/local/bin/bench watch
2025-07-13 14:25:17,695 INFO /usr/local/bin/bench worker
2025-07-13 14:25:17,788 INFO /usr/local/bin/bench schedule
2025-07-13 14:37:08,948 INFO /usr/local/bin/bench start
2025-07-13 14:37:10,810 INFO /usr/local/bin/bench worker
2025-07-13 14:37:10,851 INFO /usr/local/bin/bench serve --port 8000 --bind 127.0.0.1
2025-07-13 14:37:10,871 INFO /usr/local/bin/bench schedule
2025-07-13 14:37:11,153 INFO /usr/local/bin/bench watch
2025-07-13 14:37:19,316 INFO /usr/local/bin/bench start
2025-07-13 14:37:19,647 INFO /usr/local/bin/bench schedule
2025-07-13 14:37:19,676 INFO /usr/local/bin/bench worker
2025-07-13 14:37:19,682 INFO /usr/local/bin/bench watch
2025-07-13 14:37:19,861 INFO /usr/local/bin/bench serve --port 8000 --bind 127.0.0.1
2025-07-13 14:40:53,081 INFO /usr/local/bin/bench start
2025-07-13 14:40:53,858 INFO /usr/local/bin/bench schedule
2025-07-13 14:40:53,881 INFO /usr/local/bin/bench watch
2025-07-13 14:40:54,015 INFO /usr/local/bin/bench worker
2025-07-13 14:40:54,230 INFO /usr/local/bin/bench serve --port 8000 --bind 127.0.0.1
2025-07-13 14:55:38,855 INFO /usr/local/bin/bench setup production newsmart
2025-07-13 14:55:38,915 DEBUG sudo /usr/bin/python3 -m pip install ansible
2025-07-13 14:57:19,389 DEBUG bench setup role fail2ban
2025-07-13 14:57:19,556 INFO /usr/local/bin/bench setup role fail2ban
2025-07-13 14:57:22,680 WARNING /usr/local/bin/bench setup role fail2ban executed with exit code 1
2025-07-13 14:57:23,490 INFO A newer version of bench is available: 5.22.3 → 5.25.9
2025-07-13 14:57:23,540 WARNING bench setup role fail2ban executed with exit code 1
2025-07-13 14:57:23,540 WARNING /usr/local/bin/bench setup production newsmart executed with exit code 1
2025-07-13 14:57:24,793 INFO A newer version of bench is available: 5.22.3 → 5.25.9
2025-07-13 14:58:19,678 INFO /usr/local/bin/bench setup production newsmart --skip-setup-role
2025-07-13 14:58:19,715 WARNING /usr/local/bin/bench setup production newsmart --skip-setup-role executed with exit code 2
2025-07-13 14:58:20,457 INFO A newer version of bench is available: 5.22.3 → 5.25.9
2025-07-13 14:59:03,654 INFO /usr/local/bin/bench setup supervisor
2025-07-13 14:59:03,935 LOG Updated supervisord.conf: 'chmod' changed from '0700                       ; sockef file mode (default 0700)' to '0760'
2025-07-13 14:59:03,935 LOG Updated supervisord.conf: 'chown' changed from '' to 'root:root'
2025-07-13 14:59:10,331 INFO A newer version of bench is available: 5.22.3 → 5.25.9
2025-07-13 14:59:34,702 INFO /usr/local/bin/bench setup nginx
2025-07-13 14:59:38,014 INFO A newer version of bench is available: 5.22.3 → 5.25.9
2025-07-13 15:01:46,920 INFO /usr/local/bin/bench start
2025-07-13 15:01:47,220 INFO /usr/local/bin/bench watch
2025-07-13 15:01:47,230 INFO /usr/local/bin/bench schedule
2025-07-13 15:01:47,257 INFO /usr/local/bin/bench serve --port 8000
2025-07-13 15:01:47,301 INFO /usr/local/bin/bench worker
2025-07-13 15:12:25,327 INFO /usr/local/bin/bench restart
2025-07-13 15:12:26,140 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-07-13 15:12:26,593 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-07-13 15:12:26,594 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-07-13 15:12:27,886 INFO A newer version of bench is available: 5.22.3 → 5.25.9
2025-07-13 15:12:56,180 INFO /usr/local/bin/bench restart
2025-07-13 15:13:00,212 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-07-13 15:13:00,430 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-07-13 15:13:00,431 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-07-13 15:13:01,178 INFO A newer version of bench is available: 5.22.3 → 5.25.9
2025-07-13 15:13:11,429 INFO /usr/local/bin/bench start
2025-07-13 15:13:12,415 INFO /usr/local/bin/bench worker
2025-07-13 15:13:12,427 INFO /usr/local/bin/bench schedule
2025-07-13 15:13:12,455 INFO /usr/local/bin/bench serve --port 8000 --bind 127.0.0.1
2025-07-13 15:13:12,558 INFO /usr/local/bin/bench watch
2025-07-13 15:13:53,603 INFO /usr/local/bin/bench start
2025-07-13 15:13:53,885 INFO /usr/local/bin/bench watch
2025-07-13 15:13:53,953 INFO /usr/local/bin/bench serve --port 8000
2025-07-13 15:13:53,972 INFO /usr/local/bin/bench schedule
2025-07-13 15:13:54,009 INFO /usr/local/bin/bench worker
2025-07-13 16:19:14,180 INFO /usr/local/bin/bench get-app https://github.com/mymi14s/frappeauth_app
2025-07-13 16:19:14,334 LOG Getting frappeauth_app
2025-07-13 16:19:14,334 DEBUG cd ./apps && git clone https://github.com/mymi14s/frappeauth_app  --depth 1 --origin upstream
2025-07-13 16:19:17,953 LOG Installing frappeauth_app
2025-07-13 16:19:17,955 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/frappeauth_app 
2025-07-13 16:19:28,345 DEBUG bench build --app frappeauth_app
2025-07-13 16:19:28,497 INFO /usr/local/bin/bench build --app frappeauth_app
2025-07-13 16:26:21,194 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-07-13 16:26:21,928 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-07-13 16:26:21,928 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-07-13 16:26:22,848 INFO A newer version of bench is available: 5.22.3 → 5.25.9
2025-07-13 16:26:50,308 INFO /usr/local/bin/bench --site site1.local install-app frappeauth_app
2025-07-13 16:30:57,231 INFO /usr/local/bin/bench start
2025-07-13 16:30:57,646 INFO /usr/local/bin/bench watch
2025-07-13 16:30:57,671 INFO /usr/local/bin/bench schedule
2025-07-13 16:30:57,723 INFO /usr/local/bin/bench worker
2025-07-13 16:30:57,746 INFO /usr/local/bin/bench serve --port 8000
2025-07-13 16:50:18,315 INFO /usr/local/bin/bench build frappeauth_app
2025-07-13 16:50:36,878 INFO /usr/local/bin/bench build frappeauth_app
2025-07-13 16:51:14,274 INFO /usr/local/bin/bench build frappeauth_app
2025-07-13 16:52:25,526 INFO /usr/local/bin/bench start
2025-07-13 16:52:27,354 INFO /usr/local/bin/bench schedule
2025-07-13 16:52:27,370 INFO /usr/local/bin/bench watch
2025-07-13 16:52:27,372 INFO /usr/local/bin/bench serve --port 8000
2025-07-13 16:52:27,393 INFO /usr/local/bin/bench worker
2025-07-13 16:55:57,006 INFO /usr/local/bin/bench build
2025-07-13 20:08:08,478 INFO /usr/local/bin/bench start
2025-07-13 20:08:09,056 INFO /usr/local/bin/bench schedule
2025-07-13 20:08:09,060 INFO /usr/local/bin/bench serve --port 8000
2025-07-13 20:08:09,149 INFO /usr/local/bin/bench worker
2025-07-13 20:08:09,177 INFO /usr/local/bin/bench watch
2025-07-13 21:26:22,235 INFO /usr/local/bin/bench get-app https://github.com/mymi14s/frappeauth_app
2025-07-13 21:26:29,663 INFO App moved from apps/frappeauth_app to archived/apps/frappeauth_app-2025-07-13
2025-07-13 21:26:29,745 LOG Getting frappeauth_app
2025-07-13 21:26:29,746 DEBUG cd ./apps && git clone https://github.com/mymi14s/frappeauth_app  --depth 1 --origin upstream
2025-07-13 21:26:31,328 LOG Installing frappeauth_app
2025-07-13 21:26:31,329 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/frappeauth_app 
2025-07-13 21:26:46,361 DEBUG bench build --app frappeauth_app
2025-07-13 21:26:47,053 INFO /usr/local/bin/bench build --app frappeauth_app
2025-07-13 21:27:32,520 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-07-13 21:27:34,442 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-07-13 21:27:34,442 WARNING restarting supervisor group `frappe:` failed. Use `bench restart` to retry.
2025-07-13 21:27:37,097 INFO A newer version of bench is available: 5.22.3 → 5.25.9
2025-07-13 21:27:50,550 INFO /usr/local/bin/bench --site newsmart.local install-app frappeauth_app
2025-07-13 21:28:31,314 INFO /usr/local/bin/bench --site site1.local install-app frappeauth_app
2025-07-13 21:29:56,911 INFO /usr/local/bin/bench restart
2025-07-13 21:30:10,457 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-07-13 21:30:13,051 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-07-13 21:30:13,054 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-07-13 21:30:15,502 INFO A newer version of bench is available: 5.22.3 → 5.25.9
2025-07-13 21:30:26,811 INFO /usr/local/bin/bench start
2025-07-13 21:30:41,248 INFO /usr/local/bin/bench start --skip-redis-config-generation
2025-07-13 21:30:41,273 WARNING /usr/local/bin/bench start --skip-redis-config-generation executed with exit code 2
2025-07-13 21:30:42,566 INFO A newer version of bench is available: 5.22.3 → 5.25.9
2025-07-13 21:30:55,453 INFO /usr/local/bin/bench start
2025-07-13 22:01:29,665 INFO /usr/local/bin/bench --site site1.local list-apps
2025-07-14 13:07:39,515 INFO /usr/local/bin/bench start
2025-07-14 13:07:40,418 INFO /usr/local/bin/bench serve --port 8000
2025-07-14 13:07:40,495 INFO /usr/local/bin/bench worker
2025-07-14 13:07:40,566 INFO /usr/local/bin/bench schedule
2025-07-14 13:07:40,596 INFO /usr/local/bin/bench watch
2025-07-15 12:47:51,850 INFO /usr/local/bin/bench start
2025-07-15 12:47:53,306 INFO /usr/local/bin/bench watch
2025-07-15 12:47:53,399 INFO /usr/local/bin/bench serve --port 8000
2025-07-15 12:47:53,585 INFO /usr/local/bin/bench schedule
2025-07-15 12:47:54,185 INFO /usr/local/bin/bench worker
2025-07-15 13:09:20,853 INFO /usr/local/bin/bench status
2025-07-15 13:20:39,664 INFO /usr/local/bin/bench restart
2025-07-15 13:21:03,635 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-07-15 13:21:04,889 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-07-15 13:21:04,890 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-07-15 13:21:07,192 INFO A newer version of bench is available: 5.22.3 → 5.25.9
2025-07-15 13:21:56,052 INFO /usr/local/bin/bench start
2025-07-15 13:21:57,840 INFO /usr/local/bin/bench worker
2025-07-15 13:21:58,062 INFO /usr/local/bin/bench schedule
2025-07-15 13:21:58,193 INFO /usr/local/bin/bench watch
2025-07-15 13:21:58,385 INFO /usr/local/bin/bench serve --port 8000
2025-07-15 13:23:30,004 INFO /usr/local/bin/bench start
2025-07-15 13:29:05,192 INFO /usr/local/bin/bench --site site1.local console
2025-07-15 13:30:06,311 INFO /usr/local/bin/bench --site site1.local console
2025-07-15 14:16:22,083 INFO /usr/local/bin/bench --site site1.local console
2025-07-15 15:02:01,342 INFO /usr/local/bin/bench --site site1.local install-app smart_theme
2025-07-15 15:03:01,419 INFO /usr/local/bin/bench --site site1.local build
2025-07-15 15:11:16,897 INFO /usr/local/bin/bench start
2025-07-15 15:15:59,040 INFO /usr/local/bin/bench restart
2025-07-15 15:16:08,393 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-07-15 15:16:09,045 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-07-15 15:16:09,050 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-07-15 15:16:11,687 INFO A newer version of bench is available: 5.22.3 → 5.25.9
2025-07-15 15:16:27,247 INFO /usr/local/bin/bench start
2025-07-15 15:16:52,314 INFO /usr/local/bin/bench --site site1.local list-apps
2025-07-15 15:26:32,603 INFO /usr/local/bin/bench --site site1.local build --force
2025-07-15 15:31:47,773 INFO /usr/local/bin/bench --site site1.local build --force
2025-07-15 15:36:00,569 INFO /usr/local/bin/bench --site site1.local uninstall-app smart_theme
2025-07-15 15:37:51,998 INFO /usr/local/bin/bench --site site1.local build --force
2025-07-15 19:35:26,323 INFO /usr/local/bin/bench start
2025-07-15 19:35:26,925 INFO /usr/local/bin/bench watch
2025-07-15 19:35:26,979 INFO /usr/local/bin/bench worker
2025-07-15 19:35:26,980 INFO /usr/local/bin/bench schedule
2025-07-15 19:35:27,007 INFO /usr/local/bin/bench serve --port 8000
2025-07-15 19:42:40,031 INFO /usr/local/bin/bench console
2025-07-15 20:18:04,281 INFO /usr/local/bin/bench console
2025-07-15 20:38:25,715 INFO /usr/local/bin/bench console
2025-07-15 21:16:00,200 INFO /usr/local/bin/bench console
2025-07-16 14:23:05,930 INFO /usr/local/bin/bench start
2025-07-16 14:23:06,907 INFO /usr/local/bin/bench watch
2025-07-16 14:23:06,961 INFO /usr/local/bin/bench serve --port 8000
2025-07-16 14:23:07,137 INFO /usr/local/bin/bench schedule
2025-07-16 14:23:07,142 INFO /usr/local/bin/bench worker
2025-07-16 19:49:09,389 INFO /usr/local/bin/bench start
2025-07-16 19:49:09,904 INFO /usr/local/bin/bench serve --port 8000
2025-07-16 19:49:09,929 INFO /usr/local/bin/bench watch
2025-07-16 19:49:09,940 INFO /usr/local/bin/bench worker
2025-07-16 19:49:10,007 INFO /usr/local/bin/bench schedule
2025-07-16 20:04:04,769 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-16 20:04:50,436 INFO /usr/local/bin/bench --site site1.local build
2025-07-16 20:11:20,122 INFO /usr/local/bin/bench restart
2025-07-16 20:11:42,245 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-07-16 20:11:46,252 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-07-16 20:11:46,253 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-07-16 20:11:50,039 INFO A newer version of bench is available: 5.22.3 → 5.25.9
2025-07-16 20:12:48,924 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.install_cash_invoice.install_cash_invoice_customizations
2025-07-16 20:22:21,997 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-16 20:23:30,524 INFO /usr/local/bin/bench --site site1.local build
2025-07-16 20:33:58,306 INFO /usr/local/bin/bench --site site1.local logs
2025-07-16 20:34:33,093 INFO /usr/local/bin/bench status
2025-07-16 20:35:42,650 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-16 20:36:01,414 INFO /usr/local/bin/bench --site site1.local reload-doctype Sales Invoice
2025-07-16 20:37:14,049 INFO /usr/local/bin/bench start --skip-redis-config-generation
2025-07-16 20:37:14,624 WARNING /usr/local/bin/bench start --skip-redis-config-generation executed with exit code 2
2025-07-16 20:37:18,761 INFO A newer version of bench is available: 5.22.3 → 5.25.9
2025-07-16 20:42:16,533 INFO /usr/local/bin/bench --site site1.local build --force
2025-07-16 21:02:20,078 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-16 21:09:24,865 INFO /usr/local/bin/bench --site site1.local list-apps
2025-07-16 21:09:47,537 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_list --args ['Custom Field', {'dt': 'Sales Invoice', 'fieldname': 'is_cash'}]
2025-07-16 21:10:32,558 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_value --args ['Custom Field', 'Sales Invoice-is_cash', ['label', 'fieldtype', 'insert_after', 'default']]
2025-07-16 21:11:09,581 INFO /usr/local/bin/bench --site site1.local console --execute 
import frappe
from frappe import _
print('اختبار حالة التطبيق:')
print('1. التحقق من وجود حقل is_cash:')
custom_field = frappe.get_doc('Custom Field', 'Sales Invoice-is_cash')
print(f'   - اسم الحقل: {custom_field.fieldname}')
print(f'   - التسمية: {custom_field.label}')
print(f'   - النوع: {custom_field.fieldtype}')
print(f'   - مُدرج بعد: {custom_field.insert_after}')

print('2. التحقق من تسجيل الـ hooks:')
app_hooks = frappe.get_hooks()
js_files = app_hooks.get('app_include_js', [])
print(f'   - JavaScript files: {[f for f in js_files if "erp_optimizer_ui" in f]}')

print('3. التحقق من وجود الملفات:')
import os
js_path = '/home/<USER>/frappe-bench/apps/erp_optimizer_ui/erp_optimizer_ui/public/js/sales_invoice.js'
print(f'   - sales_invoice.js: {os.path.exists(js_path)}')
js_list_path = '/home/<USER>/frappe-bench/apps/erp_optimizer_ui/erp_optimizer_ui/public/js/sales_invoice_list.js'
print(f'   - sales_invoice_list.js: {os.path.exists(js_list_path)}')

2025-07-16 21:11:29,649 INFO /usr/local/bin/bench --site site1.local migrate --skip-failing
2025-07-16 21:14:50,272 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.install_cash_invoice.install_cash_invoice_customizations
2025-07-16 21:15:23,454 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-16 21:15:45,048 INFO /usr/local/bin/bench --site site1.local execute frappe.db.exists --args ['Client Script', 'Cash Invoice Customizations']
2025-07-16 21:16:05,901 INFO /usr/local/bin/bench --site site1.local ready-for-migration
2025-07-16 21:16:56,282 INFO /usr/local/bin/bench --site site1.local execute cash_invoice_test.test_cash_invoice_setup
2025-07-16 21:17:42,054 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.test_cash_invoice.test_cash_invoice_setup
2025-07-16 21:23:39,798 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.install_cash_invoice.install_cash_invoice_customizations
2025-07-16 21:24:25,588 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-16 21:25:20,614 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.test_list_button.test_list_button_setup
2025-07-16 21:26:57,856 INFO /usr/local/bin/bench --site site1.local build
2025-07-16 21:33:52,232 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.install_workspaces.install_workspaces
2025-07-16 21:34:53,599 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.install_workspaces.install_workspaces
2025-07-16 21:35:17,854 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-16 21:37:28,646 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.test_workspace_shortcut.test_workspace_shortcut
2025-07-16 21:38:13,176 INFO /usr/local/bin/bench --site site1.local execute frappe.db.get_list --args ['Workspace', {'module': 'erp_optimizer_ui'}]
2025-07-16 21:39:03,235 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.install_sales_workspace.install_sales_workspace
2025-07-16 21:39:45,650 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.install_sales_workspace.install_sales_workspace
2025-07-16 21:40:11,113 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.test_workspace_shortcut.test_workspace_shortcut
2025-07-17 19:33:34,146 INFO /usr/local/bin/bench start
2025-07-17 19:33:35,285 INFO /usr/local/bin/bench watch
2025-07-17 19:33:35,318 INFO /usr/local/bin/bench serve --port 8000
2025-07-17 19:33:35,498 INFO /usr/local/bin/bench schedule
2025-07-17 19:33:35,601 INFO /usr/local/bin/bench worker
2025-07-17 19:50:02,601 INFO /usr/local/bin/bench --site site1.local import-fixtures --app erp_optimizer_ui
2025-07-17 19:54:13,965 INFO /usr/local/bin/bench --site site1.local import-fixtures --app erp_optimizer_ui
2025-07-17 19:56:00,525 INFO /usr/local/bin/bench --site site1.local migrate
2025-07-17 20:07:27,995 INFO /usr/local/bin/bench --site site1.local export-fixtures
2025-07-17 20:16:45,308 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-17 20:22:39,488 INFO /usr/local/bin/bench --site site1.local execute frappe.client.get('Workspace', 'Selling')
2025-07-17 20:23:15,245 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.get_selling_workspace.get_selling_workspace
2025-07-17 20:24:02,545 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.workspace_manager.get_selling_workspace
2025-07-17 20:24:39,523 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.workspace_manager.migrate_sales_workspace
2025-07-17 20:28:32,618 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-17 20:41:01,131 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-17 20:49:59,870 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-17 20:57:44,182 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.workspace_manager.update_selling_workspace_with_subworkspaces
2025-07-17 20:59:37,348 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.install_workspaces.install_workspaces
2025-07-17 20:59:58,962 INFO /usr/local/bin/bench --site site1.local execute 
import frappe
import json
import os

# تثبيت مساحة عمل الفاتورة النقدية
cash_workspace_path = '/home/<USER>/frappe-bench/apps/erp_optimizer_ui/erp_optimizer_ui/workspace/فاتورة_نقدية.json'
with open(cash_workspace_path, 'r', encoding='utf-8') as f:
    cash_data = json.load(f)

if not frappe.db.exists('Workspace', 'فاتورة نقدية'):
    cash_doc = frappe.new_doc('Workspace')
    cash_doc.name = cash_data['name']
    cash_doc.label = cash_data['label']
    cash_doc.module = cash_data['module']
    cash_doc.public = cash_data['public']
    cash_doc.content = json.dumps(cash_data['content'], ensure_ascii=False, indent=2)
    cash_doc.insert(ignore_permissions=True)
    print('✅ تم إنشاء مساحة عمل الفاتورة النقدية')
else:
    print('⚠️ مساحة عمل الفاتورة النقدية موجودة مسبقاً')

# تثبيت مساحة عمل مرتجع المبيعات
return_workspace_path = '/home/<USER>/frappe-bench/apps/erp_optimizer_ui/erp_optimizer_ui/workspace/مرتجع_مبيعات.json'
with open(return_workspace_path, 'r', encoding='utf-8') as f:
    return_data = json.load(f)

if not frappe.db.exists('Workspace', 'مرتجع مبيعات'):
    return_doc = frappe.new_doc('Workspace')
    return_doc.name = return_data['name']
    return_doc.label = return_data['label']
    return_doc.module = return_data['module']
    return_doc.public = return_data['public']
    return_doc.content = json.dumps(return_data['content'], ensure_ascii=False, indent=2)
    return_doc.insert(ignore_permissions=True)
    print('✅ تم إنشاء مساحة عمل مرتجع المبيعات')
else:
    print('⚠️ مساحة عمل مرتجع المبيعات موجودة مسبقاً')

frappe.db.commit()

2025-07-17 21:00:52,794 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.install_subworkspaces.install_subworkspaces
2025-07-17 21:01:55,807 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.install_subworkspaces.install_subworkspaces
2025-07-17 21:02:15,665 INFO /usr/local/bin/bench --site site1.local execute 
import frappe

# إنشاء مساحة عمل الفاتورة النقدية
if not frappe.db.exists('Workspace', 'فاتورة نقدية'):
    cash_doc = frappe.new_doc('Workspace')
    cash_doc.name = 'فاتورة نقدية'
    cash_doc.label = 'فاتورة نقدية'
    cash_doc.module = 'Selling'
    cash_doc.public = 1
    cash_doc.content = '[]'
    cash_doc.insert(ignore_permissions=True)
    print('✅ تم إنشاء مساحة عمل الفاتورة النقدية')

# إنشاء مساحة عمل مرتجع المبيعات
if not frappe.db.exists('Workspace', 'مرتجع مبيعات'):
    return_doc = frappe.new_doc('Workspace')
    return_doc.name = 'مرتجع مبيعات'
    return_doc.label = 'مرتجع مبيعات'
    return_doc.module = 'Selling'
    return_doc.public = 1
    return_doc.content = '[]'
    return_doc.insert(ignore_permissions=True)
    print('✅ تم إنشاء مساحة عمل مرتجع المبيعات')

frappe.db.commit()

2025-07-17 21:03:05,655 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.workspace_manager.create_simple_subworkspaces
2025-07-17 21:03:40,978 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-17 21:06:06,389 INFO /usr/local/bin/bench --site site1.local execute 
import frappe
workspaces = frappe.get_all('Workspace', fields=['name', 'label'])
for ws in workspaces:
    print(f'- {ws.name} ({ws.label})')

2025-07-17 21:08:22,337 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.workspace_manager.create_subworkspaces_with_parent
2025-07-17 21:09:27,221 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.workspace_manager.create_subworkspaces_with_parent
2025-07-17 21:09:52,761 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-17 21:12:38,067 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-17 21:16:42,689 INFO /usr/local/bin/bench --site site1.local execute 
import frappe
import json

# فحص مساحة العمل Accounting
accounting_workspace = frappe.get_doc('Workspace', 'Accounting')
print('=== مساحة العمل Accounting ===')
print(f'Name: {accounting_workspace.name}')
print(f'Label: {accounting_workspace.label}')
print(f'Module: {accounting_workspace.module}')
print(f'Parent Page: {accounting_workspace.parent_page}')
print(f'Public: {accounting_workspace.public}')
print(f'Icon: {accounting_workspace.icon}')
print()

# عرض جميع مساحات العمل
workspaces = frappe.get_all('Workspace', fields=['name', 'label', 'module', 'parent_page'], order_by='name')
print('=== جميع مساحات العمل ===')
for ws in workspaces:
    print(f'- {ws.name} | {ws.label} | Module: {ws.module} | Parent: {ws.parent_page or "None"}')

2025-07-17 21:18:01,148 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.create_standard_workspaces.create_standard_subworkspaces
2025-07-17 21:19:21,704 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.create_standard_workspaces.create_standard_subworkspaces
2025-07-17 21:19:45,852 INFO /usr/local/bin/bench --site site1.local execute 
import frappe

# حذف مساحات العمل القديمة
old_workspaces = ['فاتورة نقدية', 'مرتجع مبيعات', 'Cash Invoice Workspace', 'Sales Return Workspace']

for ws_name in old_workspaces:
    if frappe.db.exists('Workspace', ws_name):
        try:
            frappe.delete_doc('Workspace', ws_name, ignore_permissions=True)
            print(f'🗑️ تم حذف مساحة العمل: {ws_name}')
        except Exception as e:
            print(f'⚠️ تعذر حذف مساحة العمل {ws_name}: {str(e)}')

frappe.db.commit()
print('✅ تم تنظيف مساحات العمل القديمة')

2025-07-17 21:21:09,986 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.create_standard_workspaces.create_standard_subworkspaces
2025-07-17 21:21:41,204 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-17 21:25:37,559 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.workspace_manager.list_all_workspaces
2025-07-17 21:26:29,884 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.check_workspaces.check_workspaces
2025-07-17 21:26:48,517 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.check_workspaces.create_simple_workspaces
2025-07-17 21:28:42,255 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.fix_workspace_links.fix_selling_workspace_links
2025-07-17 21:29:05,153 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.fix_workspace_links.add_content_to_subworkspaces
2025-07-17 21:29:20,138 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-17 21:31:16,720 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-17 21:36:40,200 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.create_proper_workspaces.cleanup_and_create_proper_workspaces
2025-07-17 21:37:52,657 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-17 21:44:21,666 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.create_correct_workspaces.create_correct_workspaces
2025-07-17 21:44:54,363 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-17 21:47:40,019 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-17 21:50:20,406 INFO /usr/local/bin/bench --site site1.local execute 
import frappe

# التحقق من مساحات العمل الموجودة
workspaces = frappe.get_all('Workspace', fields=['name', 'label', 'title'], 
                           filters={'name': ['in', ['cash-invoice', 'sales-return', 'فاتورة-نقدية', 'مرتجع-مبيعات']]})

print('=== مساحات العمل الموجودة ===')
for ws in workspaces:
    print(f'Name: {ws.name} | Label: {ws.label} | Title: {ws.title}')
    print(f'URL: http://192.168.8.157:8000/app/{ws.name}')
    print('---')

# البحث عن جميع مساحات العمل التي تحتوي على كلمات مفتاحية
all_workspaces = frappe.get_all('Workspace', fields=['name', 'label'])
print('\n=== جميع مساحات العمل ===')
for ws in all_workspaces:
    if any(keyword in ws.name.lower() or keyword in ws.label.lower() 
           for keyword in ['cash', 'invoice', 'return', 'فاتورة', 'نقدية', 'مرتجع']):
        print(f'Name: {ws.name} | Label: {ws.label}')

2025-07-17 21:51:11,811 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.check_workspace_urls.check_workspace_urls
2025-07-17 21:51:59,324 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.fix_workspace_urls.fix_workspace_urls
2025-07-17 21:52:16,600 INFO /usr/local/bin/bench --site site1.local execute erp_optimizer_ui.fix_workspace_urls.test_workspace_access
2025-07-17 21:52:32,760 INFO /usr/local/bin/bench --site site1.local clear-cache
2025-07-23 14:28:55,127 INFO /usr/local/bin/bench start
2025-07-23 14:28:55,767 INFO /usr/local/bin/bench watch
2025-07-23 14:28:55,916 INFO /usr/local/bin/bench serve --port 8000
2025-07-23 14:28:55,938 INFO /usr/local/bin/bench schedule
2025-07-23 14:28:55,940 INFO /usr/local/bin/bench worker
2025-07-23 14:42:52,706 INFO /usr/local/bin/bench clear-cache
2025-07-23 14:51:48,383 INFO /usr/local/bin/bench clear-cache
2025-07-23 14:53:23,226 INFO /usr/local/bin/bench --site site1.local build
2025-07-23 15:06:56,705 INFO /usr/local/bin/bench --site site1.local install-app custom_erp
2025-07-23 15:17:33,902 INFO /usr/local/bin/bench --site site1.local console
2025-07-23 15:20:16,246 INFO /usr/local/bin/bench --site site1.local migrate
2025-07-23 15:29:37,879 INFO /usr/local/bin/bench clear-cache
2025-08-03 14:27:44,635 INFO /usr/local/bin/bench start
2025-08-03 14:27:45,190 INFO /usr/local/bin/bench schedule
2025-08-03 14:27:45,195 INFO /usr/local/bin/bench worker
2025-08-03 14:27:45,204 INFO /usr/local/bin/bench serve --port 8000
2025-08-03 14:27:45,217 INFO /usr/local/bin/bench watch
2025-08-03 15:34:19,244 INFO /usr/local/bin/bench --site site1.local install-app posawesome
2025-08-03 15:36:17,242 INFO /usr/local/bin/bench --site site1.local uninstall-app posawesome
2025-08-03 15:36:50,960 INFO /usr/local/bin/bench --site site1.local install-app posawesome
2025-08-03 15:44:11,040 INFO /usr/local/bin/bench --site site1.local migrate
2025-08-03 15:46:56,878 INFO /usr/local/bin/bench --site site1.local update
2025-08-03 15:46:56,884 WARNING /usr/local/bin/bench --site site1.local update executed with exit code 2
2025-08-03 15:46:58,063 INFO A newer version of bench is available: 5.22.3 → 5.25.9
2025-08-03 15:47:13,398 INFO /usr/local/bin/bench update
2025-08-03 15:47:15,299 WARNING shallow_clone is set in your bench config.
However without passing the --reset flag, your repositories will be unshallowed.
To avoid this, cancel this operation and run `bench update --reset`.

Consider the consequences of `git reset --hard` on your apps before you run that.
To avoid seeing this warning, set shallow_clone to false in your common_site_config.json
		
2025-08-03 15:47:46,581 WARNING /usr/local/bin/bench update executed with exit code 1
2025-08-03 15:47:47,254 INFO A newer version of bench is available: 5.22.3 → 5.25.9
2025-08-03 15:51:35,576 INFO /usr/local/bin/bench --site site1.local install-app interface_customization
2025-08-03 15:55:44,365 INFO /usr/local/bin/bench --site site1.local uninstall-app interface_customization
2025-08-04 13:09:03,801 INFO /usr/local/bin/bench start
2025-08-04 13:09:04,277 INFO /usr/local/bin/bench serve --port 8000
2025-08-04 13:09:04,311 INFO /usr/local/bin/bench worker
2025-08-04 13:09:04,363 INFO /usr/local/bin/bench schedule
2025-08-04 13:09:04,402 INFO /usr/local/bin/bench watch
2025-08-04 13:16:50,578 INFO /usr/local/bin/bench --site site1.local install-app custom_erp
2025-08-04 13:25:40,830 INFO /usr/local/bin/bench restart
2025-08-04 13:26:17,385 DEBUG cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe:
2025-08-04 13:26:17,700 WARNING cd /home/<USER>/frappe-bench && sudo supervisorctl restart frappe: executed with exit code 2
2025-08-04 13:26:17,700 WARNING /usr/local/bin/bench restart executed with exit code 1
2025-08-04 13:26:20,599 INFO A newer version of bench is available: 5.22.3 → 5.25.9
2025-08-04 13:26:32,357 INFO /usr/local/bin/bench build
2025-08-04 13:29:54,837 INFO /usr/local/bin/bench start
2025-08-04 13:29:57,635 INFO /usr/local/bin/bench serve --port 8000
2025-08-04 13:29:57,659 INFO /usr/local/bin/bench worker
2025-08-04 13:29:57,857 INFO /usr/local/bin/bench schedule
2025-08-04 13:29:57,886 INFO /usr/local/bin/bench watch
2025-08-04 13:30:25,947 INFO /usr/local/bin/bench start
2025-08-04 13:31:11,439 INFO /usr/local/bin/bench status
2025-08-04 13:34:03,949 INFO /usr/local/bin/bench build --app custom_erp
2025-08-04 13:42:32,602 INFO /usr/local/bin/bench build --app custom_erp
2025-08-04 13:43:23,992 INFO /usr/local/bin/bench clear-cache
2025-08-04 13:47:57,839 INFO /usr/local/bin/bench build --app custom_erp
2025-08-04 13:50:29,283 INFO /usr/local/bin/bench start
2025-08-04 13:50:29,698 INFO /usr/local/bin/bench schedule
2025-08-04 13:50:29,702 INFO /usr/local/bin/bench worker
2025-08-04 13:50:29,739 INFO /usr/local/bin/bench watch
2025-08-04 13:50:29,747 INFO /usr/local/bin/bench serve --port 8000
