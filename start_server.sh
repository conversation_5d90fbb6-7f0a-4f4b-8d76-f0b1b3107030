#!/bin/bash

# سكريبت لتشغيل النظام على أي شبكة
# Script to start the system on any network

echo "🚀 بدء تشغيل نظام ERP..."
echo "🚀 Starting ERP System..."

# الحصول على عنوان IP الحالي
CURRENT_IP=$(hostname -I | awk '{print $1}')
echo "📍 عنوان IP الحالي: $CURRENT_IP"
echo "📍 Current IP Address: $CURRENT_IP"

# التحقق من وجود عنوان IP
if [ -z "$CURRENT_IP" ]; then
    echo "❌ لا يمكن الحصول على عنوان IP"
    echo "❌ Cannot get IP address"
    exit 1
fi

# تشغيل النظام على جميع الواجهات
echo "🌐 تشغيل النظام على جميع الواجهات الشبكية..."
echo "🌐 Starting system on all network interfaces..."

cd /home/<USER>/frappe-bench

# تشغيل النظام
bench start --host 0.0.0.0 --port 8000 &

# انتظار قليل للتأكد من بدء التشغيل
sleep 5

echo ""
echo "✅ تم تشغيل النظام بنجاح!"
echo "✅ System started successfully!"
echo ""
echo "🔗 يمكنك الوصول للنظام عبر:"
echo "🔗 You can access the system via:"
echo "   - المحلي (Local): http://localhost:8000"
echo "   - الشبكة (Network): http://$CURRENT_IP:8000"
echo ""
echo "📱 للوصول من الهاتف أو أجهزة أخرى:"
echo "📱 To access from mobile or other devices:"
echo "   http://$CURRENT_IP:8000"
echo ""
echo "⚠️  تأكد من أن جدار الحماية يسمح بالاتصال على المنفذ 8000"
echo "⚠️  Make sure firewall allows connections on port 8000"
echo ""
echo "🛑 لإيقاف النظام اضغط Ctrl+C"
echo "🛑 To stop the system press Ctrl+C"

# انتظار إشارة الإيقاف
wait
