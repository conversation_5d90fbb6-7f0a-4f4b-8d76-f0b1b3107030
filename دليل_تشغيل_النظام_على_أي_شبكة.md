# دليل تشغيل النظام على أي شبكة

## المشكلة
عندما تغير الشبكة، يتغير عنوان IP الخاص بجهازك، مما يجعل النظام غير قابل للوصول من الأجهزة الأخرى.

## الحلول المتاحة

### الحل الأول: استخدام السكريبت التلقائي (الأسهل) ⭐

```bash
# تشغيل السكريبت للحصول على IP الحالي
./get_ip.sh

# تشغيل النظام على جميع الشبكات
./start_server.sh
```

### الحل الثاني: التشغيل اليدوي

```bash
# 1. الحصول على IP الحالي
hostname -I

# 2. تشغيل النظام على جميع الواجهات
bench start --host 0.0.0.0 --port 8000
```

### الحل الثالث: تشغيل النظام بشكل دائم

```bash
# تشغيل النظام في الخلفية
nohup bench start --host 0.0.0.0 --port 8000 > server.log 2>&1 &
```

## خطوات التشغيل التفصيلية

### 1. معرفة عنوان IP الحالي

```bash
cd /home/<USER>/frappe-bench
./get_ip.sh
```

سيظهر لك شيء مثل:
```
✅ العنوان الرئيسي: *************
🔗 رابط النظام: http://*************:8000
```

### 2. تشغيل النظام

**الطريقة الأولى (السكريبت التلقائي):**
```bash
./start_server.sh
```

**الطريقة الثانية (يدوياً):**
```bash
bench start --host 0.0.0.0 --port 8000
```

### 3. الوصول للنظام

- **من نفس الجهاز:** `http://localhost:8000`
- **من أجهزة أخرى:** `http://[IP_ADDRESS]:8000`
- **مثال:** `http://*************:8000`

## إعدادات جدار الحماية

### Ubuntu/Debian:
```bash
sudo ufw allow 8000
sudo ufw reload
```

### CentOS/RHEL:
```bash
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --reload
```

## استكشاف الأخطاء

### المشكلة: لا يمكن الوصول من أجهزة أخرى

**الحلول:**
1. تأكد من تشغيل النظام على `0.0.0.0` وليس `127.0.0.1`
2. تحقق من إعدادات جدار الحماية
3. تأكد من أن الأجهزة على نفس الشبكة

### المشكلة: تغيير IP عند تغيير الشبكة

**الحل:**
```bash
# احصل على IP الجديد
./get_ip.sh

# أعد تشغيل النظام
./start_server.sh
```

### المشكلة: النظام لا يبدأ

**الحلول:**
```bash
# تحقق من حالة النظام
bench status

# امسح الذاكرة المؤقتة
bench clear-cache

# أعد بناء النظام
bench build
```

## نصائح مهمة

### 1. للاستخدام اليومي
- احفظ السكريبتات في مجلد سهل الوصول
- أنشئ اختصارات على سطح المكتب

### 2. للشبكات المختلفة
- استخدم `./get_ip.sh` في كل مرة تغير فيها الشبكة
- شارك عنوان IP الجديد مع المستخدمين الآخرين

### 3. للأمان
- لا تشغل النظام على `0.0.0.0` في الشبكات العامة
- استخدم VPN للوصول الآمن من خارج الشبكة المحلية

## أوامر مفيدة

```bash
# معرفة IP الحالي
hostname -I

# تشغيل النظام على جميع الواجهات
bench start --host 0.0.0.0 --port 8000

# تشغيل النظام في الخلفية
nohup bench start --host 0.0.0.0 --port 8000 &

# إيقاف النظام
pkill -f "bench start"

# تحقق من المنافذ المفتوحة
netstat -tlnp | grep 8000

# تحقق من حالة النظام
ps aux | grep bench
```

## الملفات المهمة

- `start_server.sh` - سكريبت تشغيل النظام
- `get_ip.sh` - سكريبت الحصول على IP
- `sites/common_site_config.json` - إعدادات النظام العامة
- `sites/site1.local/site_config.json` - إعدادات الموقع المحدد

---

**ملاحظة:** بعد تطبيق هذه الإعدادات، سيعمل النظام على أي شبكة تتصل بها دون الحاجة لتغيير الإعدادات مرة أخرى.
