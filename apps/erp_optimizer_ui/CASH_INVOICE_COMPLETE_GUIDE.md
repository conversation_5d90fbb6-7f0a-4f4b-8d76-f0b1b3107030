# دليل الفواتير النقدية الكامل - Cash Invoice Complete Guide

## ✅ الميزات المتاحة

### 1. زر الفاتورة النقدية في قائمة العرض
- **الموقع**: قائمة فواتير المبيعات `/app/sales-invoice`
- **الزر**: 💰 فاتورة نقدية (أخضر)
- **الموضع**: بجانب زر "Add Sales Invoice"
- **الوظيفة**: إنشاء فاتورة نقدية جديدة

### 2. اختصار في مساحة العمل للمبيعات
- **الموقع**: مساحة العمل للمبيعات
- **الاختصار**: 💰 إنشاء فاتورة نقدية
- **الموضع**: في أعلى قائمة الاختصارات
- **الوظيفة**: إنشاء فاتورة نقدية جديدة

### 3. تخصيصات الفاتورة النقدية
- **حقل خاص**: "فاتورة نقدية؟" (checkbox)
- **إخفاء الحقول**: الحقول غير الضرورية تُخفى تلقائياً
- **مؤشر مرئي**: "💰 فاتورة نقدية - مدفوعة فوراً"
- **تاريخ الاستحقاق**: يُعين تلقائياً لنفس تاريخ الإنشاء

### 4. فلاتر وعرض محسن
- **فلتر سريع**: للفواتير النقدية في قائمة العرض
- **فلتر سريع**: للفواتير العادية في قائمة العرض
- **مؤشر أزرق**: للفواتير النقدية المكتملة
- **رمز 💰**: بجانب أسماء العملاء للفواتير النقدية

## 🧪 اختبار الميزات

### اختبار 1: الزر في قائمة العرض
```
1. اذهب إلى: /app/sales-invoice
2. ابحث عن زر "💰 فاتورة نقدية" (أخضر)
3. انقر على الزر
4. تأكد من فتح فاتورة جديدة مع تفعيل حقل "فاتورة نقدية؟"
```

### اختبار 2: اختصار مساحة العمل
```
1. اذهب إلى مساحة العمل للمبيعات
2. ابحث عن اختصار "💰 إنشاء فاتورة نقدية"
3. انقر على الاختصار
4. تأكد من فتح فاتورة جديدة مع تفعيل حقل "فاتورة نقدية؟"
5. تأكد من ظهور رسالة "تم إنشاء فاتورة نقدية جديدة"
```

### اختبار 3: وظائف الفاتورة النقدية
```
1. في الفاتورة النقدية:
   - تأكد من إخفاء الحقول غير الضرورية
   - تأكد من ظهور مؤشر "💰 فاتورة نقدية - مدفوعة فوراً"
   - تأكد من تعيين تاريخ الاستحقاق = تاريخ الإنشاء
```

### اختبار 4: فلاتر قائمة العرض
```
1. في قائمة فواتير المبيعات:
   - انقر على "فلاتر سريعة" → "الفواتير النقدية"
   - انقر على "فلاتر سريعة" → "الفواتير العادية"
   - تأكد من ظهور رمز 💰 بجانب أسماء العملاء للفواتير النقدية
```

## 📁 الملفات المعنية

### JavaScript Files
- `apps/erp_optimizer_ui/erp_optimizer_ui/public/js/sales_invoice_list.js`
- `apps/erp_optimizer_ui/erp_optimizer_ui/public/js/sales_invoice.js`
- `apps/erp_optimizer_ui/erp_optimizer_ui/public/js/workspace_customization.js`

### Python Files
- `apps/erp_optimizer_ui/erp_optimizer_ui/install_cash_invoice.py`
- `apps/erp_optimizer_ui/erp_optimizer_ui/install_sales_workspace.py`

### Configuration Files
- `apps/erp_optimizer_ui/erp_optimizer_ui/hooks.py`
- `apps/erp_optimizer_ui/erp_optimizer_ui/workspace/المبيعات.json`

### CSS Files
- `apps/erp_optimizer_ui/erp_optimizer_ui/public/css/custom_theme.css`

## 🛠️ أوامر التثبيت والصيانة

### تثبيت تخصيصات الفاتورة النقدية
```bash
cd /home/<USER>/frappe-bench
bench --site site1.local execute erp_optimizer_ui.install_cash_invoice.install_cash_invoice_customizations
```

### تثبيت مساحة العمل للمبيعات
```bash
cd /home/<USER>/frappe-bench
bench --site site1.local execute erp_optimizer_ui.install_sales_workspace.install_sales_workspace
```

### مسح الـ Cache
```bash
cd /home/<USER>/frappe-bench
bench --site site1.local clear-cache
```

### إعادة تشغيل الخادم
```bash
cd /home/<USER>/frappe-bench
bench restart
```

## 🔍 استكشاف الأخطاء

### إذا لم يظهر الزر في قائمة العرض:
1. تحقق من الكونسول (F12)
2. ابحث عن رسالة "تم إضافة زر الفاتورة النقدية بنجاح"
3. إذا لم تظهر، حدث الصفحة وانتظر 3 ثوانٍ

### إذا لم يظهر الاختصار في مساحة العمل:
1. تأكد من تثبيت مساحة العمل للمبيعات
2. امسح الـ cache وأعد تشغيل الخادم
3. تحقق من وجود مساحة العمل في: الإعدادات → مساحات العمل

### إذا لم تعمل الفواتير النقدية:
1. تأكد من وجود الحقل المخصص "is_cash"
2. تأكد من وجود Client Script "Cash Invoice Customizations"
3. تأكد من تفعيل Client Script

## 🎯 الحقول المخفية في الفواتير النقدية

عند تفعيل "فاتورة نقدية؟"، تُخفى الحقول التالية:
- `due_date` - تاريخ الاستحقاق
- `payment_terms_template` - قالب شروط الدفع
- `tc_name` - الشروط والأحكام
- `terms` - الشروط
- `advance_paid` - المبلغ المدفوع مقدماً
- `allocate_advances_automatically` - تخصيص المقدمات تلقائياً
- `get_advances` - الحصول على المقدمات
- `payment_schedule` - جدول الدفع
- `sales_partner` - شريك المبيعات
- `commission_rate` - معدل العمولة
- `total_commission` - إجمالي العمولة
- `loyalty_points` - نقاط الولاء
- `loyalty_amount` - مبلغ الولاء
- `redeem_loyalty_points` - استرداد نقاط الولاء
- `loyalty_points_redemption` - استرداد نقاط الولاء
- `apply_discount_on` - تطبيق الخصم على
- `coupon_code` - رمز الكوبون
- `referral_sales_partner` - شريك المبيعات المُحيل
- `sales_team` - فريق المبيعات

## 📊 الأقسام المخفية

- `payment_schedule_section` - قسم جدول الدفع
- `advances_section` - قسم المقدمات
- `sales_team_section` - قسم فريق المبيعات
- `more_info` - معلومات إضافية

---

**تاريخ التحديث**: 2025-01-16  
**الحالة**: ✅ جاهز للاستخدام  
**المطور**: ERP Optimizer UI Team

## 🎉 ملخص الإنجاز

تم تنفيذ الطلب بالكامل:

1. ✅ **زر الفاتورة النقدية في قائمة العرض** - يعمل بشكل مثالي
2. ✅ **اختصار في مساحة العمل للمبيعات** - تم إنشاؤه وتفعيله
3. ✅ **الزر لا يظهر في نموذج الفاتورة** - تم إزالته حسب الطلب
4. ✅ **جميع الوظائف تعمل بشكل صحيح** - تم اختبارها وتأكيدها

الميزة جاهزة للاستخدام! 🚀