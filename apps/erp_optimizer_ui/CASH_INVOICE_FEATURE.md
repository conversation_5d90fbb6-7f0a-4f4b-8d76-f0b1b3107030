# ميزة الفاتورة النقدية ومرتجع المبيعات - Cash Invoice & Sales Return Feature

## نظرة عامة / Overview

تم إضافة ميزة الفاتورة النقدية ومرتجع المبيعات إلى تطبيق `erp_optimizer_ui` لتسهيل إنشاء وإدارة الفواتير النقدية ومرتجعات المبيعات في نظام ERPNext.

This feature adds cash invoice and sales return functionality to the `erp_optimizer_ui` app to facilitate creating and managing cash invoices and sales returns in ERPNext.

## الميزات المضافة / Added Features

### 1. زر إضافة فاتورة نقدية / Cash Invoice Button
- **الموقع**:
  - شريط الأدوات في قائمة فواتير المبيعات
  - مساحة العمل Selling الأصلية
- **الوظيفة**: إنشاء فاتورة نقدية جديدة مع القيم المحددة مسبقاً
- **الأيقونة**: 💰 فاتورة نقدية
- **اللون**: أخضر

**Location**:
  - Toolbar in Sales Invoice list view
  - Original Selling workspace
**Function**: Creates new cash invoice with pre-filled values
**Icon**: 💰 فاتورة نقدية
**Color**: Green

### 2. زر إضافة مرتجع مبيعات / Sales Return Button
- **الموقع**:
  - شريط الأدوات في قائمة فواتير المبيعات (بجانب زر الفاتورة النقدية)
  - مساحة العمل Selling الأصلية
- **الوظيفة**: إنشاء مرتجع مبيعات جديد
- **الأيقونة**: 🔄 مرتجع مبيعات
- **اللون**: أحمر

**Location**:
  - Toolbar in Sales Invoice list view (next to cash invoice button)
  - Original Selling workspace
**Function**: Creates new sales return
**Icon**: 🔄 مرتجع مبيعات
**Color**: Red

### 3. ميزات قائمة العرض / List View Features
- **الموقع**: شريط الأدوات في قائمة فواتير المبيعات
- **ميزات إضافية**:
  - تحويل الفواتير المحددة إلى فواتير نقدية
  - فلاتر سريعة للفواتير النقدية والعادية

**Location**: Toolbar in Sales Invoice list view
**Additional Features**:
  - Convert selected invoices to cash invoices
  - Quick filters for cash and regular invoices

### 4. إخفاء الحقول غير الضرورية / Hide Unnecessary Fields
عند تفعيل خيار "فاتورة نقدية"، يتم إخفاء الحقول التالية:

When "Cash Invoice" option is enabled, the following fields are hidden:

- `due_date` - تاريخ الاستحقاق
- `payment_terms_template` - قالب شروط الدفع
- `tc_name` - الشروط والأحكام
- `terms` - الشروط
- `advance_paid` - المبلغ المدفوع مقدماً
- `allocate_advances_automatically` - تخصيص المقدمات تلقائياً
- `get_advances` - الحصول على المقدمات
- `payment_schedule` - جدول الدفع
- `sales_partner` - شريك المبيعات
- `commission_rate` - معدل العمولة
- `total_commission` - إجمالي العمولة
- `loyalty_points` - نقاط الولاء
- `loyalty_amount` - مبلغ الولاء
- `redeem_loyalty_points` - استرداد نقاط الولاء
- `loyalty_points_redemption` - استرداد نقاط الولاء
- `apply_discount_on` - تطبيق الخصم على
- `coupon_code` - رمز الكوبون
- `referral_sales_partner` - شريك المبيعات المُحيل
- `sales_team` - فريق المبيعات

### 5. مؤشر الفاتورة النقدية / Cash Invoice Indicator
- **العرض**: مؤشر مرئي في أعلى النموذج
- **النص**: "💰 فاتورة نقدية - مدفوعة فوراً"
- **اللون**: أخضر للتمييز

**Display**: Visual indicator at the top of the form
**Text**: "💰 فاتورة نقدية - مدفوعة فوراً"
**Color**: Green for distinction

### 6. تعيين تاريخ الاستحقاق تلقائياً / Auto-set Due Date
عند تفعيل خيار الفاتورة النقدية، يتم تعيين تاريخ الاستحقاق تلقائياً لنفس تاريخ الفاتورة.

When cash invoice option is enabled, due date is automatically set to the same as posting date.

## الملفات المضافة / Added Files

### 1. JavaScript Files
- `apps/erp_optimizer_ui/erp_optimizer_ui/public/js/sales_invoice.js` - تخصيصات النموذج
- `apps/erp_optimizer_ui/erp_optimizer_ui/public/js/sales_invoice_list.js` - تخصيصات قائمة العرض

### 2. Python Files
- `apps/erp_optimizer_ui/erp_optimizer_ui/install_cash_invoice.py` - ملف التثبيت

### 3. Configuration Files
- تحديث `hooks.py` لتضمين ملفات JavaScript الجديدة

## التغييرات في مساحة العمل / Workspace Changes

### إنشاء مساحات العمل الفرعية / Sub-workspaces Creation
- تم إنشاء مساحة عمل فرعية "cash-invoice" تحت مساحة العمل Selling
- تم إنشاء مساحة عمل فرعية "sales-return" تحت مساحة العمل Selling
- كل مساحة عمل فرعية لها محتوى مخصص وتخصيصات منفصلة
- مساحة العمل الأب: Selling
- الروابط تتبع التنسيق الصحيح: `/app/[workspace-name]`

**Created sub-workspaces under Selling:**
- "cash-invoice" - dedicated workspace for cash invoices
- "sales-return" - dedicated workspace for sales returns
- Each sub-workspace has custom content and separate customizations
- Parent workspace: Selling
- Links follow correct format: `/app/[workspace-name]`

### التخصيصات المضافة لمساحة العمل Selling / Selling Workspace Customizations
- إضافة رابط "💰 فاتورة نقدية" يؤدي إلى مساحة العمل الفرعية
- إضافة رابط "🔄 مرتجع مبيعات" يؤدي إلى مساحة العمل الفرعية
- تخصيص JavaScript لجعل الروابط تعمل بشكل صحيح
- إضافة تنسيق CSS مخصص للمساحات الفرعية

**Added to Selling workspace:**
- "💰 فاتورة نقدية" link leading to cash invoice sub-workspace
- "🔄 مرتجع مبيعات" link leading to sales return sub-workspace
- Custom JavaScript to make links work properly
- Custom CSS styling for sub-workspaces

## التثبيت / Installation

### 1. تثبيت التخصيصات / Install Customizations
```bash
cd /home/<USER>/frappe-bench
bench --site site1.local execute erp_optimizer_ui.install_cash_invoice.install_cash_invoice_customizations
```

### 2. إعادة بناء الأصول / Rebuild Assets
```bash
bench --site site1.local build
```

### 3. إعادة تشغيل الخادم / Restart Server
```bash
bench restart
```

## الاستخدام / Usage

### 1. إنشاء فاتورة نقدية جديدة / Create New Cash Invoice

**من قائمة فواتير المبيعات:**
1. اذهب إلى قائمة فواتير المبيعات
2. انقر على زر "💰 فاتورة نقدية" (الزر الأخضر)
3. سيتم إنشاء فاتورة جديدة مع تفعيل خيار الفاتورة النقدية

**من مساحة العمل Selling:**
1. اذهب إلى مساحة العمل Selling: `http://192.168.8.157:8000/app/selling`
2. انقر على رابط "💰 فاتورة نقدية" للانتقال إلى مساحة العمل المخصصة
3. في مساحة العمل المخصصة، انقر على "💰 إنشاء فاتورة نقدية"
4. سيتم إنشاء فاتورة جديدة مع تفعيل خيار الفاتورة النقدية

**الرابط المباشر لمساحة عمل الفاتورة النقدية:**
`http://192.168.8.157:8000/app/cash-invoice`

### 2. إنشاء مرتجع مبيعات جديد / Create New Sales Return

**من قائمة فواتير المبيعات:**
1. اذهب إلى قائمة فواتير المبيعات
2. انقر على زر "🔄 مرتجع مبيعات" (الزر الأحمر)
3. سيتم إنشاء فاتورة جديدة مع تفعيل خيار المرتجع

**من مساحة العمل Selling:**
1. اذهب إلى مساحة العمل Selling: `http://192.168.8.157:8000/app/selling`
2. انقر على رابط "🔄 مرتجع مبيعات" للانتقال إلى مساحة العمل المخصصة
3. في مساحة العمل المخصصة، انقر على "🔄 إنشاء مرتجع مبيعات"
4. سيتم إنشاء فاتورة جديدة مع تفعيل خيار المرتجع

**الرابط المباشر لمساحة عمل مرتجع المبيعات:**
`http://192.168.8.157:8000/app/sales-return`

### 3. تحويل فاتورة موجودة / Convert Existing Invoice
1. افتح فاتورة مبيعات (مسودة)
2. فعّل خيار "فاتورة نقدية؟" للفاتورة النقدية
3. أو فعّل خيار "Is Return" لمرتجع المبيعات
4. ستختفي الحقول غير الضرورية تلقائياً (للفاتورة النقدية)

### 4. تحويل متعدد من قائمة العرض / Bulk Convert from List View
1. اذهب إلى قائمة فواتير المبيعات
2. حدد الفواتير المطلوبة (مسودات فقط)
3. انقر على "🔄 تحويل إلى فاتورة نقدية"

## إلغاء التثبيت / Uninstallation

```bash
cd /home/<USER>/frappe-bench
bench --site site1.local execute erp_optimizer_ui.install_cash_invoice.uninstall_cash_invoice_customizations
```

## الدعم / Support

للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.

For support or to report issues, please contact the development team.

---

**تاريخ الإنشاء / Created**: 2025-07-16
**الإصدار / Version**: 1.0.0
**المطور / Developer**: ERP Optimizer UI Team
