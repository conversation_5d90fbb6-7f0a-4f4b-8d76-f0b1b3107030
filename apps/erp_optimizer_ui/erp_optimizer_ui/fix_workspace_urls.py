#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import frappe
import json

def fix_workspace_urls():
    """إصلاح روابط مساحات العمل لتتطابق مع الأسماء الفعلية"""
    
    print("🔧 إصلاح روابط مساحات العمل...")
    
    try:
        # الحصول على مساحة العمل Selling
        selling_workspace = frappe.get_doc("Workspace", "Selling")
        
        # تحويل المحتوى الحالي إلى قائمة
        if selling_workspace.content:
            content = json.loads(selling_workspace.content)
        else:
            content = []
        
        # البحث عن الروابط الخاطئة وإصلاحها
        for item in content:
            if item.get('type') == 'link':
                # إصلاح رابط الفاتورة النقدية
                if item.get('label') == '💰 فاتورة نقدية' and item.get('link_to') == 'cash-invoice':
                    item['link_to'] = 'فاتورة نقدية'
                    print("✅ تم إصلاح رابط الفاتورة النقدية")
                
                # إصلاح رابط مرتجع المبيعات
                elif item.get('label') == '🔄 مرتجع مبيعات' and item.get('link_to') == 'sales-return':
                    item['link_to'] = 'مرتجع مبيعات'
                    print("✅ تم إصلاح رابط مرتجع المبيعات")
        
        # تحديث المحتوى
        selling_workspace.content = json.dumps(content, ensure_ascii=False, indent=2)
        selling_workspace.save(ignore_permissions=True)
        frappe.db.commit()
        
        print("✅ تم إصلاح روابط مساحة العمل Selling")
        
        # طباعة الروابط الصحيحة
        print("\n=== الروابط الصحيحة ===")
        print("1. مساحة العمل Selling:")
        print("   http://192.168.8.157:8000/app/Selling")
        print("2. مساحة عمل الفاتورة النقدية:")
        print("   http://192.168.8.157:8000/app/فاتورة%20نقدية")
        print("   أو: http://192.168.8.157:8000/app/%D9%81%D8%A7%D8%AA%D9%88%D8%B1%D8%A9%20%D9%86%D9%82%D8%AF%D9%8A%D8%A9")
        print("3. مساحة عمل مرتجع المبيعات:")
        print("   http://192.168.8.157:8000/app/مرتجع%20مبيعات")
        print("   أو: http://192.168.8.157:8000/app/%D9%85%D8%B1%D8%AA%D8%AC%D8%B9%20%D9%85%D8%A8%D9%8A%D8%B9%D8%A7%D8%AA")
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الروابط: {str(e)}")
        import traceback
        traceback.print_exc()

def test_workspace_access():
    """اختبار الوصول لمساحات العمل"""
    
    print("\n🧪 اختبار الوصول لمساحات العمل...")
    
    workspaces_to_test = [
        "Selling",
        "فاتورة نقدية", 
        "مرتجع مبيعات"
    ]
    
    for ws_name in workspaces_to_test:
        try:
            if frappe.db.exists('Workspace', ws_name):
                ws_doc = frappe.get_doc('Workspace', ws_name)
                print(f"✅ {ws_name} - موجودة ويمكن الوصول إليها")
                print(f"   Label: {ws_doc.label}")
                print(f"   Parent: {ws_doc.parent_page or 'None'}")
            else:
                print(f"❌ {ws_name} - غير موجودة")
        except Exception as e:
            print(f"❌ {ws_name} - خطأ في الوصول: {str(e)}")

if __name__ == "__main__":
    fix_workspace_urls()
    test_workspace_access()
