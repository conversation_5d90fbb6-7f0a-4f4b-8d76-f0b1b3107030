// تخصيص الـ workspace
frappe.provide('erp_optimizer_ui');

erp_optimizer_ui.customize_workspace = function () {
    // تطبيق التخصيصات على الـ workspace
    $(document).on('page-change', function () {
        if (frappe.get_route()[0] === 'workspace') {
            setTimeout(function () {
                // تخصيص العناوين
                $('.workspace-title').css({
                    'font-family': 'Cairo, sans-serif',
                    'font-weight': 'bold',
                    'color': '#007bff'
                });

                // تخصيص الأيقونات
                $('.shortcut-widget-box .widget-title').css({
                    'font-family': 'Cairo, sans-serif',
                    'font-weight': 'bold'
                });

                // تخصيص البطاقات
                $('.links-widget-box').css({
                    'border-radius': '8px',
                    'box-shadow': '0 2px 5px rgba(0,0,0,0.05)',
                    'transition': 'all 0.3s ease'
                });

                $('.links-widget-box').hover(
                    function () {
                        $(this).css({
                            'box-shadow': '0 5px 15px rgba(0,0,0,0.1)',
                            'transform': 'translateY(-2px)'
                        });
                    },
                    function () {
                        $(this).css({
                            'box-shadow': '0 2px 5px rgba(0,0,0,0.05)',
                            'transform': 'translateY(0)'
                        });
                    }
                );

                // تخصيص القائمة الجانبية
                $('.desk-sidebar .standard-sidebar-item.selected').css({
                    'background-color': '#007bff',
                    'color': 'white'
                });

                // إضافة أيقونات للروابط
                $('.widget-content .link-item').each(function () {
                    let text = $(this).text().trim();
                    let icon = '';

                    if (text.includes('فاتورة') || text.includes('Invoice')) {
                        icon = '📄 ';
                    } else if (text.includes('عميل') || text.includes('Customer')) {
                        icon = '👤 ';
                    } else if (text.includes('مورد') || text.includes('Supplier')) {
                        icon = '🏭 ';
                    } else if (text.includes('صنف') || text.includes('Item')) {
                        icon = '📦 ';
                    } else if (text.includes('مخزون') || text.includes('Stock')) {
                        icon = '🏪 ';
                    } else if (text.includes('تقرير') || text.includes('Report')) {
                        icon = '📊 ';
                    } else if (text.includes('دفع') || text.includes('Payment')) {
                        icon = '💰 ';
                    } else {
                        icon = '🔗 ';
                    }

                    $(this).html(icon + text);
                });

                // إعداد اختصارات الفاتورة النقدية ومرتجع المبيعات
                setup_cash_invoice_workspace_shortcut();
                setup_sales_return_workspace_shortcut();
            }, 500);
        }
    });
};

// دالة إعداد اختصار الفاتورة النقدية في مساحة العمل
function setup_cash_invoice_workspace_shortcut() {
    // مراقبة النقر على اختصار الفاتورة النقدية
    $(document).off('click.cash_invoice_shortcut').on('click.cash_invoice_shortcut', 'a[data-link-to="Sales Invoice"]', function (e) {
        // التحقق من أن هذا هو اختصار الفاتورة النقدية
        let linkText = $(this).text().trim();
        let linkLabel = $(this).find('.link-label').text().trim();

        if (linkText.includes('💰 إنشاء فاتورة نقدية') ||
            linkLabel.includes('💰 إنشاء فاتورة نقدية') ||
            linkText.includes('إنشاء فاتورة نقدية')) {

            e.preventDefault();
            e.stopPropagation();

            // إنشاء فاتورة نقدية جديدة
            create_cash_invoice_from_workspace();

            return false;
        }
    });
}

// دالة إعداد اختصار مرتجع المبيعات في مساحة العمل
function setup_sales_return_workspace_shortcut() {
    // مراقبة النقر على اختصار مرتجع المبيعات
    $(document).off('click.sales_return_shortcut').on('click.sales_return_shortcut', 'a[data-link-to="Sales Invoice"]', function (e) {
        // التحقق من أن هذا هو اختصار مرتجع المبيعات
        let linkText = $(this).text().trim();
        let linkLabel = $(this).find('.link-label').text().trim();

        if (linkText.includes('🔄 إنشاء مرتجع مبيعات') ||
            linkLabel.includes('🔄 إنشاء مرتجع مبيعات') ||
            linkText.includes('إنشاء مرتجع مبيعات')) {

            e.preventDefault();
            e.stopPropagation();

            // إنشاء مرتجع مبيعات جديد
            create_sales_return_from_workspace();

            return false;
        }
    });
}

// دالة إنشاء فاتورة نقدية من مساحة العمل
function create_cash_invoice_from_workspace() {
    // إنشاء فاتورة نقدية جديدة مع القيم المحددة مسبقاً
    frappe.new_doc('Sales Invoice', {
        'is_cash': 1,
        'due_date': frappe.datetime.get_today(),
        'posting_date': frappe.datetime.get_today()
    });

    // رسالة تأكيد
    frappe.show_alert({
        message: __('تم إنشاء فاتورة نقدية جديدة'),
        indicator: 'green'
    });

    console.log('تم إنشاء فاتورة نقدية من مساحة العمل');
}

// دالة إنشاء مرتجع مبيعات من مساحة العمل
function create_sales_return_from_workspace() {
    // إنشاء مرتجع مبيعات جديد مع القيم المحددة مسبقاً
    frappe.new_doc('Sales Invoice', {
        'is_return': 1,
        'is_cash': 0,
        'posting_date': frappe.datetime.get_today()
    });

    // رسالة تأكيد
    frappe.show_alert({
        message: __('تم إنشاء مرتجع مبيعات جديد'),
        indicator: 'orange'
    });

    console.log('تم إنشاء مرتجع مبيعات من مساحة العمل');
}

// تنفيذ التخصيصات عند تحميل الصفحة
$(document).ready(function () {
    erp_optimizer_ui.customize_workspace();
});