// تخصيصات مساحات العمل الفرعية - Sub-workspace Customizations
// تخصيص مساحات عمل الفاتورة النقدية ومرتجع المبيعات

frappe.ready(function () {
    // تخصيص مساحات العمل الفرعية
    customize_subworkspaces();

    // مراقبة تغيير الصفحات
    $(document).on('page-change', function () {
        setTimeout(function () {
            customize_subworkspaces();
        }, 500);
    });
});

function customize_subworkspaces() {
    let current_route = frappe.get_route();

    // التحقق من مساحة عمل الفاتورة النقدية
    if (current_route[0] === 'فاتورة نقدية') {
        customize_cash_invoice_workspace();
    }

    // التحقق من مساحة عمل مرتجع المبيعات
    if (current_route[0] === 'مرتجع مبيعات') {
        customize_sales_return_workspace();
    }
}

function customize_cash_invoice_workspace() {
    console.log('تخصيص مساحة عمل الفاتورة النقدية...');

    // تخصيص روابط إنشاء الفاتورة النقدية
    setTimeout(function () {
        // البحث عن روابط إنشاء فاتورة نقدية
        let cash_invoice_links = $('a[href*="Sales Invoice"], .shortcut-widget-box, .workspace-link').filter(function () {
            return $(this).text().includes('💰 إنشاء فاتورة نقدية') ||
                $(this).text().includes('إنشاء فاتورة نقدية') ||
                $(this).find('.widget-title').text().includes('إنشاء فاتورة نقدية');
        });

        cash_invoice_links.each(function () {
            let link = $(this);

            // إزالة الأحداث السابقة
            link.off('click.cash_invoice_workspace');

            // إضافة حدث مخصص
            link.on('click.cash_invoice_workspace', function (e) {
                e.preventDefault();
                e.stopPropagation();

                console.log('تم النقر على رابط إنشاء فاتورة نقدية من مساحة العمل الفرعية');

                // إنشاء فاتورة نقدية جديدة
                create_cash_invoice_from_subworkspace();

                return false;
            });
        });

        // إضافة تنسيق مخصص
        add_cash_invoice_workspace_styling();

    }, 1000);
}

function customize_sales_return_workspace() {
    console.log('تخصيص مساحة عمل مرتجع المبيعات...');

    // تخصيص روابط إنشاء مرتجع المبيعات
    setTimeout(function () {
        // البحث عن روابط إنشاء مرتجع مبيعات
        let sales_return_links = $('a[href*="Sales Invoice"], .shortcut-widget-box, .workspace-link').filter(function () {
            return $(this).text().includes('🔄 إنشاء مرتجع مبيعات') ||
                $(this).text().includes('إنشاء مرتجع مبيعات') ||
                $(this).find('.widget-title').text().includes('إنشاء مرتجع مبيعات');
        });

        sales_return_links.each(function () {
            let link = $(this);

            // إزالة الأحداث السابقة
            link.off('click.sales_return_workspace');

            // إضافة حدث مخصص
            link.on('click.sales_return_workspace', function (e) {
                e.preventDefault();
                e.stopPropagation();

                console.log('تم النقر على رابط إنشاء مرتجع مبيعات من مساحة العمل الفرعية');

                // إنشاء مرتجع مبيعات جديد
                create_sales_return_from_subworkspace();

                return false;
            });
        });

        // إضافة تنسيق مخصص
        add_sales_return_workspace_styling();

    }, 1000);
}

// دالة إنشاء فاتورة نقدية من مساحة العمل الفرعية
function create_cash_invoice_from_subworkspace() {
    frappe.new_doc('Sales Invoice', {
        'is_cash': 1,
        'is_return': 0,
        'is_pos': 1,
        'due_date': frappe.datetime.get_today(),
        'posting_date': frappe.datetime.get_today()
    });

    // رسالة تأكيد
    frappe.show_alert({
        message: __('تم إنشاء فاتورة نقدية جديدة من مساحة العمل المخصصة'),
        indicator: 'green'
    });

    console.log('تم إنشاء فاتورة نقدية من مساحة العمل الفرعية');
}

// دالة إنشاء مرتجع مبيعات من مساحة العمل الفرعية
function create_sales_return_from_subworkspace() {
    frappe.new_doc('Sales Invoice', {
        'is_return': 1,
        'is_cash': 0,
        'update_stock': 1,
        'posting_date': frappe.datetime.get_today()
    });

    // رسالة تأكيد
    frappe.show_alert({
        message: __('تم إنشاء مرتجع مبيعات جديد من مساحة العمل المخصصة'),
        indicator: 'orange'
    });

    console.log('تم إنشاء مرتجع مبيعات من مساحة العمل الفرعية');
}

// إضافة تنسيق CSS لمساحة عمل الفاتورة النقدية
function add_cash_invoice_workspace_styling() {
    if (!$('#cash-invoice-workspace-css').length) {
        $('head').append(`
            <style id="cash-invoice-workspace-css">
                .workspace-container[data-workspace="فاتورة نقدية"] {
                    background: linear-gradient(135deg, #f8fff8 0%, #e8f5e8 100%);
                }

                .workspace-container[data-workspace="فاتورة نقدية"] .shortcut-widget-box {
                    border-left: 4px solid #28a745;
                    transition: all 0.3s ease;
                }

                .workspace-container[data-workspace="فاتورة نقدية"] .shortcut-widget-box:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
                }

                .workspace-container[data-workspace="فاتورة نقدية"] .page-title {
                    color: #28a745;
                    font-weight: bold;
                }
            </style>
        `);
    }
}

// إضافة تنسيق CSS لمساحة عمل مرتجع المبيعات
function add_sales_return_workspace_styling() {
    if (!$('#sales-return-workspace-css').length) {
        $('head').append(`
            <style id="sales-return-workspace-css">
                .workspace-container[data-workspace="sales-return"] {
                    background: linear-gradient(135deg, #fff8f8 0%, #f5e8e8 100%);
                }

                .workspace-container[data-workspace="sales-return"] .shortcut-widget-box {
                    border-left: 4px solid #dc3545;
                    transition: all 0.3s ease;
                }

                .workspace-container[data-workspace="sales-return"] .shortcut-widget-box:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.2);
                }

                .workspace-container[data-workspace="sales-return"] .page-title {
                    color: #dc3545;
                    font-weight: bold;
                }
            </style>
        `);
    }
}

// تخصيص إضافي للتأكد من عمل الروابط في جميع الحالات
$(document).on('click', '.shortcut-widget-box, .workspace-link, a', function (e) {
    let text = $(this).text() || $(this).find('.widget-title').text();

    if (text.includes('💰 إنشاء فاتورة نقدية') || text.includes('إنشاء فاتورة نقدية')) {
        e.preventDefault();
        e.stopPropagation();

        create_cash_invoice_from_subworkspace();
        return false;
    }

    if (text.includes('🔄 إنشاء مرتجع مبيعات') || text.includes('إنشاء مرتجع مبيعات')) {
        e.preventDefault();
        e.stopPropagation();

        create_sales_return_from_subworkspace();
        return false;
    }
});
