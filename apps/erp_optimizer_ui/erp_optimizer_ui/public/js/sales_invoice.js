
frappe.ui.form.on('Sales Invoice', {
    refresh: function (frm) {
        // ➊ أزِل الأزرار المكررة إن وُجِدت
        const cash_btn = '📥 تعبئة كـ فاتورة نقدية';
        const return_btn = '🔄 مرتجع مبيعات';

        if (frm.page.has_inner_button && frm.page.has_inner_button(cash_btn)) {
            frm.page.remove_inner_button(cash_btn);
        }
        if (frm.page.has_inner_button && frm.page.has_inner_button(return_btn)) {
            frm.page.remove_inner_button(return_btn);
        }

        // ➋ زر "فاتورة نقدية" (فقط للمسودات)
        if (frm.doc.docstatus === 0) {
            frm.page.add_inner_button(cash_btn, () => make_cash_invoice(frm));
        }

        // ➌ زر "مرتجع مبيعات" (فقط للمسودات)
        if (frm.doc.docstatus === 0) {
            frm.page.add_inner_button(return_btn, () => make_sales_return(frm));
        }

        // إخفاء الحقول غير الضرورية إذا كانت فاتورة نقدية
        if (frm.doc.is_cash) {
            hide_unnecessary_fields_for_cash_invoice(frm);
            show_cash_invoice_indicator(frm);
        }
    },

    onload: function (frm) {
        // تطبيق التخصيصات عند تحميل النموذج
        if (frm.doc.is_cash) {
            hide_unnecessary_fields_for_cash_invoice(frm);
        }
    },

    is_cash: function (frm) {
        // عند تغيير حالة الفاتورة النقدية
        if (frm.doc.is_cash) {
            hide_unnecessary_fields_for_cash_invoice(frm);
            show_cash_invoice_indicator(frm);
            // تعيين تاريخ الاستحقاق لنفس تاريخ الفاتورة
            frm.set_value('due_date', frm.doc.posting_date);
        } else {
            show_all_fields_for_regular_invoice(frm);
            hide_cash_invoice_indicator(frm);
        }
    },

    customer: function (frm) {
        if (frm.doc.customer) {
            frappe.call({
                method: "frappe.client.get_list",
                args: {
                    doctype: "Sales Invoice",
                    filters: { customer: frm.doc.customer },
                    fields: ["name"]
                },
                callback: function (r) {
                    let invoice_count = r.message.length;
                    frappe.call({
                        method: "frappe.client.get_list",
                        args: {
                            doctype: "Payment Entry",
                            filters: { party: frm.doc.customer },
                            fields: ["name"]
                        },
                        callback: function (res) {
                            let payment_count = res.message.length;
                            frappe.msgprint({
                                title: "معلومات العميل",
                                indicator: "blue",
                                message: `🔔 <b>${frm.doc.customer}</b><br>
                                    🧾 عدد الفواتير: <b>${invoice_count}</b><br>
                                    💰 عدد السندات: <b>${payment_count}</b><br>
                                    <br><a href='/app/sales-invoice?customer=${frm.doc.customer}' target='_blank'>عرض الفواتير</a><br>
                                    <a href='/app/payment-entry?party=${frm.doc.customer}' target='_blank'>عرض السندات</a>`
                            });
                        }
                    });
                }
            });
        }
    }
});

/* ---------- الدوال المساعدة ---------- */

// يحوّل النموذج إلى فاتورة نقدية جديدة
function make_cash_invoice(frm) {
    // تعيين القيم الأساسية للفاتورة النقدية
    frm.set_value({
        is_cash: 1,
        is_pos: 1,
        due_date: frappe.datetime.get_today(),
        posting_date: frappe.datetime.get_today()
    });

    // إخفاء الحقول غير الضرورية
    hide_fields(frm, [
        'due_date', 'po_date', 'customer_po_no',
        'payment_terms_template', 'set_posting_time',
        'advance_paid', 'allocate_advances_automatically', 'get_advances',
        'payment_schedule', 'sales_partner', 'commission_rate', 'total_commission',
        'loyalty_points', 'loyalty_amount', 'redeem_loyalty_points',
        'loyalty_points_redemption', 'apply_discount_on',
        'coupon_code', 'referral_sales_partner', 'sales_team'
    ]);

    // إضافة صف دفع نقدي إذا كان هناك مبلغ إجمالي
    if (frm.doc.grand_total && frm.doc.grand_total > 0) {
        frm.clear_table('payments');
        frm.add_child('payments', {
            mode_of_payment: 'Cash',
            amount: frm.doc.grand_total
        });
        frm.refresh_fields();
    }

    // إظهار مؤشر الفاتورة النقدية
    show_cash_invoice_indicator(frm);

    frappe.show_alert('✅ تم تجهيز فاتورة مبيعات نقدية');
}

// يحوّل النموذج إلى مرتجع مبيعات
function make_sales_return(frm) {
    // تعيين القيم الأساسية لمرتجع المبيعات
    frm.set_value({
        is_return: 1,        // يُعرِّفها كمرتجع
        is_cash: 0,          // إلغاء الفاتورة النقدية
        update_stock: 1,     // التأثير على المخزون
        posting_date: frappe.datetime.get_today()
    });

    // إخفاء الحقول غير الضرورية للمرتجع
    hide_fields(frm, [
        'due_date', 'po_date', 'customer_po_no',
        'payment_terms_template', 'set_posting_time',
        'advance_paid', 'allocate_advances_automatically', 'get_advances',
        'payment_schedule', 'sales_partner', 'commission_rate', 'total_commission',
        'loyalty_points', 'loyalty_amount', 'redeem_loyalty_points',
        'loyalty_points_redemption', 'apply_discount_on',
        'coupon_code', 'referral_sales_partner', 'sales_team'
    ]);

    // أفرغ جدول الأصناف ليُدخل المُستخدم الكميات المرتجَعة فقط
    frm.clear_table('items');
    frm.refresh_fields();

    // إظهار مؤشر مرتجع المبيعات
    frm.dashboard.set_headline(
        `🔄 ${__('مرتجع مبيعات')} - ${__('أدخل الأصناف المرتجعة')}`
    );

    frappe.show_alert('✅ تم تجهيز مرتجع المبيعات – أدخِل الأصناف المرتجَعة');
}

// إخفاء مجموعة من الحقول
function hide_fields(frm, field_list) {
    field_list.forEach(field_name => {
        try {
            let field = frappe.meta.get_docfield('Sales Invoice', field_name, frm.docname);
            if (field) {
                field.hidden = 1;
            }
            // إخفاء الحقل في النموذج أيضاً
            frm.toggle_display(field_name, false);
        } catch (e) {
            console.log(`تعذر إخفاء الحقل: ${field_name}`);
        }
    });
    frm.refresh();
}

// دالة إخفاء الحقول غير الضرورية للفاتورة النقدية
function hide_unnecessary_fields_for_cash_invoice(frm) {
    const fields_to_hide = [
        'due_date', 'payment_terms_template', 'tc_name', 'terms',
        'advance_paid', 'allocate_advances_automatically', 'get_advances',
        'payment_schedule', 'payment_schedule_section', 'advances_section',
        'sales_partner', 'commission_rate', 'total_commission',
        'loyalty_points', 'loyalty_amount', 'redeem_loyalty_points',
        'loyalty_points_redemption', 'apply_discount_on',
        'coupon_code', 'referral_sales_partner', 'sales_team'
    ];

    fields_to_hide.forEach(field => {
        frm.toggle_display(field, false);
    });

    // إخفاء أقسام كاملة
    frm.toggle_display('payment_schedule_section', false);
    frm.toggle_display('advances_section', false);
    frm.toggle_display('sales_team_section', false);
    frm.toggle_display('more_info', false);
}

// دالة إظهار جميع الحقول للفاتورة العادية
function show_all_fields_for_regular_invoice(frm) {
    const fields_to_show = [
        'due_date', 'payment_terms_template', 'tc_name', 'terms',
        'advance_paid', 'allocate_advances_automatically', 'get_advances',
        'payment_schedule', 'payment_schedule_section', 'advances_section',
        'sales_partner', 'commission_rate', 'total_commission',
        'loyalty_points', 'loyalty_amount', 'redeem_loyalty_points',
        'loyalty_points_redemption', 'apply_discount_on',
        'coupon_code', 'referral_sales_partner', 'sales_team'
    ];

    fields_to_show.forEach(field => {
        frm.toggle_display(field, true);
    });

    // إظهار الأقسام
    frm.toggle_display('payment_schedule_section', true);
    frm.toggle_display('advances_section', true);
    frm.toggle_display('sales_team_section', true);
    frm.toggle_display('more_info', true);
}

// دالة إظهار مؤشر الفاتورة النقدية
function show_cash_invoice_indicator(frm) {
    if (!frm.doc.__islocal) {
        frm.dashboard.set_headline(
            `💰 ${__('فاتورة نقدية')} - ${__('مدفوعة فوراً')}`
        );
    }
}

// دالة إخفاء مؤشر الفاتورة النقدية
function hide_cash_invoice_indicator(frm) {
    if (!frm.doc.__islocal) {
        frm.dashboard.clear_headline();
    }
}
