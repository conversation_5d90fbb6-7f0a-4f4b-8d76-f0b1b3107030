// تخصيص قائمة فواتير المبيعات - إضافة زر الفاتورة النقدية
// Sales Invoice List Customization - Add Cash Invoice Button

frappe.listview_settings['Sales Invoice'] = {
    add_fields: ["customer", "customer_name", "base_grand_total", "outstanding_amount", "due_date", "company", "currency", "is_return", "is_cash"],

    get_indicator: function (doc) {
        const status_colors = {
            "Draft": "grey",
            "Unpaid": "orange",
            "Paid": "green",
            "Return": "gray",
            "Credit Note Issued": "gray",
            "Unpaid and Discounted": "orange",
            "Partly Paid and Discounted": "yellow",
            "Overdue and Discounted": "red",
            "Overdue": "red",
            "Partly Paid": "yellow",
            "Internal Transfer": "darkgrey"
        };

        // إضافة مؤشر خاص للفواتير النقدية
        if (doc.is_cash && doc.docstatus === 1) {
            return [__("فاتورة نقدية"), "blue", "is_cash,=,1"];
        }

        if (doc.status) {
            return [__(doc.status), status_colors[doc.status] || "grey", "status,=," + doc.status];
        }
    },

    onload: function (listview) {
        // إضافة زر إنشاء فاتورة نقدية بجانب زر Add Sales Invoice
        setTimeout(() => {
            add_cash_invoice_button_to_toolbar(listview);
        }, 1000);

        // إضافة زر تحويل الفواتير المحددة إلى نقدية (للفواتير المسودة)
        listview.page.add_action_item(__('🔄 تحويل إلى فاتورة نقدية'), function () {
            convert_to_cash_invoice(listview);
        });

        // إضافة فلتر سريع للفواتير النقدية
        listview.page.add_inner_button(__('الفواتير النقدية'), function () {
            listview.filter_area.add(listview.doctype, 'is_cash', '=', 1);
        }, __('فلاتر سريعة'));

        // إضافة فلتر سريع للفواتير العادية
        listview.page.add_inner_button(__('الفواتير العادية'), function () {
            listview.filter_area.add(listview.doctype, 'is_cash', '=', 0);
        }, __('فلاتر سريعة'));
    },

    // تخصيص عرض البيانات
    prepare_data: function (data) {
        // إضافة رمز للفواتير النقدية
        if (data.is_cash) {
            data.customer_name = `💰 ${data.customer_name || data.customer}`;
        }
    },

    // تخصيص الأعمدة المعروضة
    formatters: {
        customer_name: function (value, field, doc) {
            if (doc.is_cash) {
                return `<span class="cash-invoice-indicator">💰</span> ${value}`;
            }
            return value;
        },
        base_grand_total: function (value, field, doc) {
            let formatted = format_currency(value, doc.currency);
            if (doc.is_cash) {
                return `<span class="text-success"><strong>${formatted}</strong></span>`;
            }
            return formatted;
        }
    }
};

// دالة إضافة زر الفاتورة النقدية بجانب زر Add Sales Invoice
function add_cash_invoice_button_to_toolbar(listview) {
    // التحقق من عدم وجود الزر مسبقاً
    if ($('.btn-cash-invoice').length > 0) {
        return;
    }

    // البحث عن الزر الرئيسي
    let primary_btn = listview.page.btn_primary;

    if (primary_btn && primary_btn.length) {
        // إنشاء زر الفاتورة النقدية
        let cash_invoice_btn = $(`
            <button class="btn btn-success btn-cash-invoice btn-sm"
                    style="margin-left: 8px;"
                    title="إنشاء فاتورة نقدية جديدة">
                💰 فاتورة نقدية
            </button>
        `);

        // إضافة حدث النقر
        cash_invoice_btn.on('click', function (e) {
            e.preventDefault();
            e.stopPropagation();
            create_new_cash_invoice();
        });

        // إدراج الزر بجانب الزر الرئيسي
        primary_btn.after(cash_invoice_btn);

        console.log('تم إضافة زر الفاتورة النقدية بنجاح');
    } else {
        console.log('لم يتم العثور على الزر الرئيسي');
        // محاولة أخرى بعد ثانية
        setTimeout(() => {
            add_cash_invoice_button_to_toolbar(listview);
        }, 1000);
    }
}

// دالة مراقبة إضافة الزر عند التحديث
function add_cash_invoice_button_observer(listview) {
    // مراقبة بسيطة لإعادة إضافة الزر عند التحديث
    setInterval(function () {
        if ($('.btn-cash-invoice').length === 0) {
            add_cash_invoice_button_to_toolbar(listview);
        }
    }, 3000);
}

// دالة إنشاء فاتورة نقدية جديدة
function create_new_cash_invoice() {
    frappe.new_doc('Sales Invoice', {
        'is_cash': 1,
        'due_date': frappe.datetime.get_today(),
        'posting_date': frappe.datetime.get_today()
    });
}

// دالة تحويل الفواتير المحددة إلى فواتير نقدية
function convert_to_cash_invoice(listview) {
    let selected_docs = listview.get_checked_items();

    if (selected_docs.length === 0) {
        frappe.msgprint(__('يرجى تحديد فاتورة واحدة على الأقل'));
        return;
    }

    // التحقق من أن جميع الفواتير المحددة هي مسودات
    let draft_docs = selected_docs.filter(doc => doc.docstatus === 0);
    if (draft_docs.length !== selected_docs.length) {
        frappe.msgprint(__('يمكن تحويل المسودات فقط إلى فواتير نقدية'));
        return;
    }

    frappe.confirm(
        __('هل تريد تحويل {0} فاتورة إلى فواتير نقدية؟', [selected_docs.length]),
        function () {
            // تحويل الفواتير المحددة
            selected_docs.forEach(function (doc) {
                frappe.call({
                    method: 'frappe.client.set_value',
                    args: {
                        doctype: 'Sales Invoice',
                        name: doc.name,
                        fieldname: {
                            'is_cash': 1,
                            'due_date': doc.posting_date || frappe.datetime.get_today()
                        }
                    },
                    callback: function (r) {
                        if (!r.exc) {
                            frappe.show_alert({
                                message: __('تم تحويل الفاتورة {0} إلى فاتورة نقدية', [doc.name]),
                                indicator: 'green'
                            });
                        }
                    }
                });
            });

            // تحديث القائمة بعد ثانيتين
            setTimeout(function () {
                listview.refresh();
            }, 2000);
        }
    );
}

// إضافة CSS مخصص للفواتير النقدية
frappe.ready(function () {
    if (!$('#cash-invoice-list-css').length) {
        $('head').append(`
            <style id="cash-invoice-list-css">
                .cash-invoice-indicator {
                    color: #28a745;
                    font-weight: bold;
                    margin-right: 5px;
                }

                .list-row[data-is-cash="1"] {
                    background-color: #f8fff8;
                    border-left: 3px solid #28a745;
                }

                .list-row[data-is-cash="1"]:hover {
                    background-color: #f0fff0;
                }

                .btn-cash-invoice {
                    background-color: #28a745 !important;
                    border-color: #28a745 !important;
                    color: white !important;
                    margin-left: 8px;
                }

                .btn-cash-invoice:hover {
                    background-color: #218838 !important;
                    border-color: #1e7e34 !important;
                    color: white !important;
                }

                .btn-sales-return {
                    background-color: #dc3545 !important;
                    border-color: #dc3545 !important;
                    color: white !important;
                    margin-left: 8px;
                }

                .btn-sales-return:hover {
                    background-color: #c82333 !important;
                    border-color: #bd2130 !important;
                    color: white !important;
                }
            </style>
        `);
    }

    // إضافة أزرار الفاتورة النقدية ومرتجع المبيعات عند تحميل صفحة قائمة فواتير المبيعات
    if (window.location.pathname.includes('/app/sales-invoice')) {
        // محاولات متعددة لإضافة الأزرار
        setTimeout(function () {
            add_custom_buttons_to_sales_invoice_list();
        }, 500);

        setTimeout(function () {
            add_custom_buttons_to_sales_invoice_list();
        }, 1500);

        setTimeout(function () {
            add_custom_buttons_to_sales_invoice_list();
        }, 3000);

        // مراقبة التغييرات في الصفحة مع تحسينات
        let observer = new MutationObserver(function (mutations) {
            let shouldAddButtons = false;
            mutations.forEach(function (mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // التحقق من إضافة عناصر جديدة قد تحتوي على أزرار
                    for (let node of mutation.addedNodes) {
                        if (node.nodeType === 1 && // Element node
                            (node.classList && (
                                node.classList.contains('page-actions') ||
                                node.classList.contains('page-head-content') ||
                                node.querySelector && node.querySelector('.btn-primary')
                            ))) {
                            shouldAddButtons = true;
                            break;
                        }
                    }
                }
            });

            if (shouldAddButtons) {
                setTimeout(function () {
                    add_custom_buttons_to_sales_invoice_list();
                }, 300);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // مراقبة تغيير الصفحة
        $(window).on('hashchange', function () {
            setTimeout(function () {
                add_custom_buttons_to_sales_invoice_list();
            }, 1000);
        });
    }
});

// إضافة مراقب لتغيير الصفحات في Frappe
$(document).on('page-change', function () {
    if (window.location.pathname.includes('/app/sales-invoice')) {
        setTimeout(function () {
            add_custom_buttons_to_sales_invoice_list();
        }, 1000);
    }
});

// مراقب إضافي لـ frappe router
if (typeof frappe !== 'undefined' && frappe.router) {
    frappe.router.on('change', function () {
        if (frappe.get_route()[0] === 'List' && frappe.get_route()[1] === 'Sales Invoice') {
            setTimeout(function () {
                add_custom_buttons_to_sales_invoice_list();
            }, 1500);
        }
    });
}

// مراقب دوري للتأكد من وجود الأزرار
setInterval(function () {
    if (window.location.pathname.includes('/app/sales-invoice') &&
        window.location.hash.includes('List/Sales%20Invoice') &&
        ($('.btn-cash-invoice').length === 0 || $('.btn-sales-return').length === 0)) {
        add_custom_buttons_to_sales_invoice_list();
    }
}, 3000);

// دالة إضافة الأزرار المخصصة إلى قائمة فواتير المبيعات
function add_custom_buttons_to_sales_invoice_list() {
    // التحقق من أننا في صفحة قائمة فواتير المبيعات
    if (!window.location.pathname.includes('/app/sales-invoice')) {
        return;
    }

    console.log('محاولة إضافة الأزرار المخصصة...');

    // البحث عن زر Add Sales Invoice
    let add_btn = find_add_button();

    if (add_btn && add_btn.length) {
        console.log('تم العثور على زر Add:', add_btn.text().trim());

        // إضافة زر الفاتورة النقدية
        add_cash_invoice_button(add_btn);

        // إضافة زر مرتجع المبيعات
        add_sales_return_button(add_btn);

    } else {
        console.log('لم يتم العثور على زر Add. الأزرار المتاحة:', $('.btn-primary').map(function () {
            return $(this).text().trim();
        }).get());
    }
}

// دالة البحث عن زر Add Sales Invoice
function find_add_button() {
    let add_btn = null;
    let selectors = [
        '.page-actions .btn-primary',
        '.page-head-content .btn-primary',
        '.list-toolbar .btn-primary',
        '.page-title .btn-primary',
        '.btn-primary[data-label="Add Sales Invoice"]',
        '.btn-primary:contains("Add Sales Invoice")',
        '.btn-primary:contains("Add")',
        '.btn-primary:contains("إضافة")',
        '.primary-action',
        '[data-original-title*="Add"]'
    ];

    // جرب كل selector
    for (let selector of selectors) {
        add_btn = $(selector).first();
        if (add_btn.length > 0) {
            console.log('تم العثور على زر Add باستخدام:', selector);
            break;
        }
    }

    // البحث بالأيقونة إذا لم نجد شيء
    if (!add_btn || !add_btn.length) {
        add_btn = $('.btn-primary').filter(function () {
            let text = $(this).text().toLowerCase();
            let hasIcon = $(this).find('svg, i.fa-plus, i.octicon-plus').length > 0;
            return hasIcon || text.includes('add') || text.includes('إضافة');
        }).first();
    }

    return add_btn;
}

// دالة إضافة زر الفاتورة النقدية
function add_cash_invoice_button(add_btn) {
    // التحقق من عدم وجود الزر مسبقاً
    if ($('.btn-cash-invoice').length > 0) {
        return;
    }

    // إنشاء زر الفاتورة النقدية
    let cash_invoice_btn = $(`
        <button class="btn btn-success btn-cash-invoice btn-sm"
                style="margin-left: 8px;"
                title="إنشاء فاتورة نقدية جديدة">
            💰 فاتورة نقدية
        </button>
    `);

    // إضافة حدث النقر
    cash_invoice_btn.on('click', function (e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('تم النقر على زر الفاتورة النقدية');
        create_new_cash_invoice();
    });

    // إدراج الزر بجانب زر Add Sales Invoice
    add_btn.after(cash_invoice_btn);
    console.log('تم إضافة زر الفاتورة النقدية');
}

// دالة إضافة زر مرتجع المبيعات
function add_sales_return_button(add_btn) {
    // التحقق من عدم وجود الزر مسبقاً
    if ($('.btn-sales-return').length > 0) {
        return;
    }

    // إنشاء زر مرتجع المبيعات
    let sales_return_btn = $(`
        <button class="btn btn-danger btn-sales-return btn-sm"
                style="margin-left: 8px;"
                title="إنشاء مرتجع مبيعات جديد">
            🔄 مرتجع مبيعات
        </button>
    `);

    // إضافة حدث النقر
    sales_return_btn.on('click', function (e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('تم النقر على زر مرتجع المبيعات');
        create_new_sales_return();
    });

    // إدراج الزر بجانب زر الفاتورة النقدية أو زر Add
    let target_btn = $('.btn-cash-invoice').length > 0 ? $('.btn-cash-invoice') : add_btn;
    target_btn.after(sales_return_btn);
    console.log('تم إضافة زر مرتجع المبيعات');
}

// دالة إنشاء فاتورة نقدية جديدة
function create_new_cash_invoice() {
    frappe.new_doc('Sales Invoice', {
        'is_cash': 1,
        'is_return': 0,
        'due_date': frappe.datetime.get_today(),
        'posting_date': frappe.datetime.get_today()
    });
}

// دالة إنشاء مرتجع مبيعات جديد
function create_new_sales_return() {
    frappe.new_doc('Sales Invoice', {
        'is_return': 1,
        'is_cash': 0,
        'posting_date': frappe.datetime.get_today()
    });
}
