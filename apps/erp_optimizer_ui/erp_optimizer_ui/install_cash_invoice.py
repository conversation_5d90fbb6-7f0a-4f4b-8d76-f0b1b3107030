#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import frappe
from frappe import _

def install_cash_invoice_customizations():
    """
    تثبيت تخصيصات الفاتورة النقدية
    Install Cash Invoice Customizations
    """
    
    # إنشاء الحقل المخصص إذا لم يكن موجوداً
    create_cash_invoice_field()
    
    # تطبيق التخصيصات
    apply_customizations()
    
    # إنشاء Client Script للتخصيصات
    create_client_script()
    
    frappe.db.commit()
    frappe.msgprint(_("تم تثبيت تخصيصات الفاتورة النقدية بنجاح"))

def create_cash_invoice_field():
    """إنشاء حقل الفاتورة النقدية"""
    
    # التحقق من وجود الحقل
    if not frappe.db.exists("Custom Field", "Sales Invoice-is_cash"):
        custom_field = frappe.get_doc({
            "doctype": "Custom Field",
            "dt": "Sales Invoice",
            "fieldname": "is_cash",
            "label": "فاتورة نقدية؟",
            "fieldtype": "Check",
            "insert_after": "due_date",
            "default": "0",
            "in_list_view": 1,
            "in_standard_filter": 1,
            "description": "تحديد ما إذا كانت هذه فاتورة نقدية أم لا"
        })
        custom_field.insert()
        frappe.db.commit()

def apply_customizations():
    """تطبيق التخصيصات الإضافية"""
    
    # إضافة Property Setter لتحسين عرض الحقل
    if not frappe.db.exists("Property Setter", "Sales Invoice-is_cash-bold"):
        property_setter = frappe.get_doc({
            "doctype": "Property Setter",
            "doctype_or_field": "DocField",
            "doc_type": "Sales Invoice",
            "field_name": "is_cash",
            "property": "bold",
            "value": "1",
            "property_type": "Check"
        })
        property_setter.insert()

def create_client_script():
    """إنشاء Client Script للتخصيصات"""
    
    script_name = "Cash Invoice Customizations"
    
    # حذف النص البرمجي القديم إن وجد
    if frappe.db.exists("Client Script", script_name):
        frappe.delete_doc("Client Script", script_name)
    
    # إنشاء النص البرمجي الجديد
    client_script = frappe.get_doc({
        "doctype": "Client Script",
        "name": script_name,
        "dt": "Sales Invoice",
        "view": "Form",
        "enabled": 1,
        "script": """
// تخصيصات الفاتورة النقدية
frappe.ui.form.on('Sales Invoice', {
    refresh: function(frm) {
        // زر إنشاء فاتورة نقدية متاح فقط في قائمة العرض
        
        // إخفاء الحقول غير الضرورية إذا كانت فاتورة نقدية
        if (frm.doc.is_cash) {
            hide_unnecessary_fields_for_cash_invoice(frm);
            show_cash_invoice_indicator(frm);
        }
    },
    
    is_cash: function(frm) {
        // عند تغيير حالة الفاتورة النقدية
        if (frm.doc.is_cash) {
            hide_unnecessary_fields_for_cash_invoice(frm);
            show_cash_invoice_indicator(frm);
            // تعيين تاريخ الاستحقاق لنفس تاريخ الفاتورة
            frm.set_value('due_date', frm.doc.posting_date);
        } else {
            show_all_fields_for_regular_invoice(frm);
            hide_cash_invoice_indicator(frm);
        }
    }
});

// دالة إنشاء فاتورة نقدية - متاحة فقط في قائمة العرض

// دالة إخفاء الحقول غير الضرورية للفاتورة النقدية
function hide_unnecessary_fields_for_cash_invoice(frm) {
    const fields_to_hide = [
        'due_date', 'payment_terms_template', 'tc_name', 'terms',
        'advance_paid', 'allocate_advances_automatically', 'get_advances',
        'payment_schedule', 'sales_partner', 'commission_rate', 'total_commission',
        'loyalty_points', 'loyalty_amount', 'redeem_loyalty_points',
        'loyalty_points_redemption', 'apply_discount_on',
        'coupon_code', 'referral_sales_partner', 'sales_team'
    ];

    fields_to_hide.forEach(field => {
        frm.toggle_display(field, false);
    });
    
    // إخفاء أقسام كاملة
    frm.toggle_display('payment_schedule_section', false);
    frm.toggle_display('advances_section', false);
    frm.toggle_display('sales_team_section', false);
}

// دالة إظهار جميع الحقول للفاتورة العادية
function show_all_fields_for_regular_invoice(frm) {
    const fields_to_show = [
        'due_date', 'payment_terms_template', 'tc_name', 'terms',
        'advance_paid', 'allocate_advances_automatically', 'get_advances',
        'payment_schedule', 'sales_partner', 'commission_rate', 'total_commission',
        'loyalty_points', 'loyalty_amount', 'redeem_loyalty_points',
        'loyalty_points_redemption', 'apply_discount_on',
        'coupon_code', 'referral_sales_partner', 'sales_team'
    ];

    fields_to_show.forEach(field => {
        frm.toggle_display(field, true);
    });
    
    // إظهار الأقسام
    frm.toggle_display('payment_schedule_section', true);
    frm.toggle_display('advances_section', true);
    frm.toggle_display('sales_team_section', true);
}

// دالة إظهار مؤشر الفاتورة النقدية
function show_cash_invoice_indicator(frm) {
    if (!frm.doc.__islocal) {
        frm.dashboard.set_headline(
            `💰 ${__('فاتورة نقدية')} - ${__('مدفوعة فوراً')}`
        );
    }
}

// دالة إخفاء مؤشر الفاتورة النقدية
function hide_cash_invoice_indicator(frm) {
    if (!frm.doc.__islocal) {
        frm.dashboard.clear_headline();
    }
}
"""
    })
    
    client_script.insert()
    frappe.db.commit()

def uninstall_cash_invoice_customizations():
    """
    إلغاء تثبيت تخصيصات الفاتورة النقدية
    Uninstall Cash Invoice Customizations
    """
    
    # حذف Client Script
    if frappe.db.exists("Client Script", "Cash Invoice Customizations"):
        frappe.delete_doc("Client Script", "Cash Invoice Customizations")
    
    # حذف Property Setter
    if frappe.db.exists("Property Setter", "Sales Invoice-is_cash-bold"):
        frappe.delete_doc("Property Setter", "Sales Invoice-is_cash-bold")
    
    # حذف Custom Field (اختياري - قد تريد الاحتفاظ بالبيانات)
    # if frappe.db.exists("Custom Field", "Sales Invoice-is_cash"):
    #     frappe.delete_doc("Custom Field", "Sales Invoice-is_cash")
    
    frappe.db.commit()
    frappe.msgprint(_("تم إلغاء تثبيت تخصيصات الفاتورة النقدية"))

if __name__ == "__main__":
    install_cash_invoice_customizations()
