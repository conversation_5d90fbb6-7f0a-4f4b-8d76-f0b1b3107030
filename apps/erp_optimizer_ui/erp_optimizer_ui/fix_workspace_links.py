#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import frappe
import json

def fix_selling_workspace_links():
    """إصلاح روابط مساحة العمل Selling"""
    
    try:
        print("🔄 إصلاح روابط مساحة العمل Selling...")
        
        # الحصول على مساحة العمل Selling
        selling_workspace = frappe.get_doc("Workspace", "Selling")
        
        # تحويل المحتوى الحالي إلى قائمة
        if selling_workspace.content:
            content = json.loads(selling_workspace.content)
        else:
            content = []
        
        # إزالة جميع الروابط القديمة للفاتورة النقدية ومرتجع المبيعات
        content = [item for item in content if not (
            item.get('label', '').startswith('💰') or
            item.get('label', '').startswith('🔄') or
            'فاتورة نقدية' in item.get('label', '') or
            'مرتجع مبيعات' in item.get('label', '') or
            'Cash Invoice' in item.get('link_to', '') or
            'Sales Return' in item.get('link_to', '')
        )]
        
        # إضافة الروابط الصحيحة لمساحات العمل الفرعية
        subworkspace_links = [
            {
                "type": "link",
                "label": "💰 فاتورة نقدية",
                "link_type": "Page",
                "link_to": "Workspaces/فاتورة نقدية",
                "dependencies": "",
                "description": "مساحة عمل مخصصة للفواتير النقدية",
                "onboard": 0,
                "is_query_report": 0,
                "icon": "money",
                "hidden": 0
            },
            {
                "type": "link", 
                "label": "🔄 مرتجع مبيعات",
                "link_type": "Page",
                "link_to": "Workspaces/مرتجع مبيعات",
                "dependencies": "",
                "description": "مساحة عمل مخصصة لمرتجعات المبيعات",
                "onboard": 0,
                "is_query_report": 0,
                "icon": "return",
                "hidden": 0
            }
        ]
        
        # إضافة الروابط في بداية القائمة
        for link in reversed(subworkspace_links):
            content.insert(0, link)
            print(f"✅ تم إضافة رابط مساحة العمل: {link['label']}")
        
        # تحديث المحتوى
        selling_workspace.content = json.dumps(content, ensure_ascii=False, indent=2)
        selling_workspace.save(ignore_permissions=True)
        frappe.db.commit()
        
        print("✅ تم إصلاح روابط مساحة العمل Selling")
        
        # طباعة الروابط الصحيحة للاختبار
        print("\n=== الروابط الصحيحة للاختبار ===")
        print("1. مساحة العمل Selling: http://***********57:8000/app/workspaces/Selling")
        print("2. مساحة عمل الفاتورة النقدية: http://***********57:8000/app/workspaces/فاتورة%20نقدية")
        print("3. مساحة عمل مرتجع المبيعات: http://***********57:8000/app/workspaces/مرتجع%20مبيعات")
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح روابط مساحة العمل: {str(e)}")
        import traceback
        traceback.print_exc()

def add_content_to_subworkspaces():
    """إضافة محتوى لمساحات العمل الفرعية"""
    
    try:
        print("🔄 إضافة محتوى لمساحات العمل الفرعية...")
        
        # محتوى مساحة عمل الفاتورة النقدية
        cash_content = [
            {
                "type": "header",
                "data": {
                    "text": "💰 إدارة الفواتير النقدية",
                    "col": 12
                }
            },
            {
                "type": "spacer"
            },
            {
                "type": "shortcut",
                "data": {
                    "shortcut_name": "New Cash Invoice",
                    "color": "green",
                    "format": "{} فاتورة نقدية جديدة",
                    "link_to": "Sales Invoice",
                    "type": "DocType",
                    "doc_view": "New",
                    "label": "💰 إنشاء فاتورة نقدية",
                    "description": "إنشاء فاتورة نقدية جديدة"
                }
            },
            {
                "type": "shortcut",
                "data": {
                    "shortcut_name": "Cash Invoices",
                    "color": "blue",
                    "format": "{} فاتورة نقدية",
                    "link_to": "Sales Invoice",
                    "type": "DocType",
                    "doc_view": "List",
                    "label": "📋 الفواتير النقدية",
                    "description": "عرض جميع الفواتير النقدية"
                }
            }
        ]
        
        # تحديث مساحة عمل الفاتورة النقدية
        cash_workspace = frappe.get_doc("Workspace", "فاتورة نقدية")
        cash_workspace.content = json.dumps(cash_content, ensure_ascii=False, indent=2)
        cash_workspace.save(ignore_permissions=True)
        print("✅ تم تحديث محتوى مساحة عمل الفاتورة النقدية")
        
        # محتوى مساحة عمل مرتجع المبيعات
        return_content = [
            {
                "type": "header",
                "data": {
                    "text": "🔄 إدارة مرتجعات المبيعات",
                    "col": 12
                }
            },
            {
                "type": "spacer"
            },
            {
                "type": "shortcut",
                "data": {
                    "shortcut_name": "New Sales Return",
                    "color": "red",
                    "format": "{} مرتجع مبيعات جديد",
                    "link_to": "Sales Invoice",
                    "type": "DocType",
                    "doc_view": "New",
                    "label": "🔄 إنشاء مرتجع مبيعات",
                    "description": "إنشاء مرتجع مبيعات جديد"
                }
            },
            {
                "type": "shortcut",
                "data": {
                    "shortcut_name": "Sales Returns",
                    "color": "orange",
                    "format": "{} مرتجع مبيعات",
                    "link_to": "Sales Invoice",
                    "type": "DocType",
                    "doc_view": "List",
                    "label": "📋 مرتجعات المبيعات",
                    "description": "عرض جميع مرتجعات المبيعات"
                }
            }
        ]
        
        # تحديث مساحة عمل مرتجع المبيعات
        return_workspace = frappe.get_doc("Workspace", "مرتجع مبيعات")
        return_workspace.content = json.dumps(return_content, ensure_ascii=False, indent=2)
        return_workspace.save(ignore_permissions=True)
        print("✅ تم تحديث محتوى مساحة عمل مرتجع المبيعات")
        
        frappe.db.commit()
        print("✅ تم حفظ جميع التغييرات")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة محتوى مساحات العمل: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    fix_selling_workspace_links()
    add_content_to_subworkspaces()
