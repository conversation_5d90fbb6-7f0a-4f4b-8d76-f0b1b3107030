#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import frappe
import json
import os

def create_correct_workspaces():
    """إنشاء مساحات العمل بالطريقة الصحيحة مع URL صحيح"""
    
    print("🔄 إنشاء مساحات العمل بالطريقة الصحيحة...")
    
    # حذف مساحات العمل الخاطئة
    cleanup_all_incorrect_workspaces()
    
    # إنشاء مساحات العمل الصحيحة
    create_cash_invoice_workspace_correct()
    create_sales_return_workspace_correct()
    
    # تحديث مساحة العمل Selling
    update_selling_workspace_correct()
    
    print("✅ تم إنشاء مساحات العمل بالطريقة الصحيحة")

def cleanup_all_incorrect_workspaces():
    """حذف جميع مساحات العمل الخاطئة"""
    
    incorrect_workspaces = [
        'فاتورة نقدية', 'مرتجع مبيعات', 
        '💰 فاتورة نقدية', '🔄 مرتجع مبيعات',
        'Cash Invoice', 'Sales Return',
        'Cash Invoice Workspace', 'Sales Return Workspace',
        'Cash Invoice Management', 'Sales Return Management'
    ]
    
    for ws_name in incorrect_workspaces:
        if frappe.db.exists('Workspace', ws_name):
            try:
                frappe.delete_doc('Workspace', ws_name, ignore_permissions=True)
                print(f'🗑️ تم حذف مساحة العمل: {ws_name}')
            except Exception as e:
                print(f'⚠️ تعذر حذف مساحة العمل {ws_name}: {str(e)}')
    
    frappe.db.commit()
    print('✅ تم تنظيف جميع مساحات العمل الخاطئة')

def create_cash_invoice_workspace_correct():
    """إنشاء مساحة عمل الفاتورة النقدية بالطريقة الصحيحة"""
    
    try:
        workspace_name = "cash-invoice"  # اسم بسيط بدون مسافات
        
        if frappe.db.exists("Workspace", workspace_name):
            frappe.delete_doc("Workspace", workspace_name, ignore_permissions=True)
            print(f"🗑️ تم حذف مساحة العمل السابقة: {workspace_name}")
        
        # إنشاء مساحة العمل
        workspace = frappe.new_doc("Workspace")
        workspace.name = workspace_name
        workspace.label = "فاتورة نقدية"
        workspace.title = "فاتورة نقدية"
        workspace.module = "Selling"
        workspace.parent_page = "Selling"
        workspace.public = 1
        workspace.icon = "money"
        workspace.indicator_color = "green"
        
        # محتوى مساحة العمل
        content = [
            {
                "type": "header",
                "data": {
                    "text": "💰 إدارة الفواتير النقدية",
                    "col": 12
                }
            },
            {
                "type": "spacer"
            },
            {
                "type": "shortcut",
                "data": {
                    "shortcut_name": "New Cash Invoice",
                    "color": "green",
                    "format": "{} فاتورة نقدية جديدة",
                    "link_to": "Sales Invoice",
                    "type": "DocType",
                    "doc_view": "New",
                    "label": "💰 إنشاء فاتورة نقدية",
                    "description": "إنشاء فاتورة نقدية جديدة"
                }
            },
            {
                "type": "shortcut",
                "data": {
                    "shortcut_name": "Cash Invoices List",
                    "color": "blue",
                    "format": "{} فاتورة نقدية",
                    "link_to": "Sales Invoice",
                    "type": "DocType",
                    "doc_view": "List",
                    "label": "📋 قائمة الفواتير النقدية",
                    "description": "عرض جميع الفواتير النقدية"
                }
            },
            {
                "type": "shortcut",
                "data": {
                    "shortcut_name": "POS Profile",
                    "color": "orange",
                    "format": "{} نقطة بيع",
                    "link_to": "POS Profile",
                    "type": "DocType",
                    "doc_view": "List",
                    "label": "🏪 نقاط البيع",
                    "description": "إدارة نقاط البيع"
                }
            }
        ]
        
        workspace.content = json.dumps(content, ensure_ascii=False, indent=2)
        workspace.insert(ignore_permissions=True)
        frappe.db.commit()
        
        print(f"✅ تم إنشاء مساحة عمل الفاتورة النقدية: {workspace_name}")
        print(f"🔗 الرابط الصحيح: http://*************:8000/app/{workspace_name}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء مساحة عمل الفاتورة النقدية: {str(e)}")
        import traceback
        traceback.print_exc()

def create_sales_return_workspace_correct():
    """إنشاء مساحة عمل مرتجع المبيعات بالطريقة الصحيحة"""
    
    try:
        workspace_name = "sales-return"  # اسم بسيط بدون مسافات
        
        if frappe.db.exists("Workspace", workspace_name):
            frappe.delete_doc("Workspace", workspace_name, ignore_permissions=True)
            print(f"🗑️ تم حذف مساحة العمل السابقة: {workspace_name}")
        
        # إنشاء مساحة العمل
        workspace = frappe.new_doc("Workspace")
        workspace.name = workspace_name
        workspace.label = "مرتجع مبيعات"
        workspace.title = "مرتجع مبيعات"
        workspace.module = "Selling"
        workspace.parent_page = "Selling"
        workspace.public = 1
        workspace.icon = "return"
        workspace.indicator_color = "red"
        
        # محتوى مساحة العمل
        content = [
            {
                "type": "header",
                "data": {
                    "text": "🔄 إدارة مرتجعات المبيعات",
                    "col": 12
                }
            },
            {
                "type": "spacer"
            },
            {
                "type": "shortcut",
                "data": {
                    "shortcut_name": "New Sales Return",
                    "color": "red",
                    "format": "{} مرتجع مبيعات جديد",
                    "link_to": "Sales Invoice",
                    "type": "DocType",
                    "doc_view": "New",
                    "label": "🔄 إنشاء مرتجع مبيعات",
                    "description": "إنشاء مرتجع مبيعات جديد"
                }
            },
            {
                "type": "shortcut",
                "data": {
                    "shortcut_name": "Sales Returns List",
                    "color": "orange",
                    "format": "{} مرتجع مبيعات",
                    "link_to": "Sales Invoice",
                    "type": "DocType",
                    "doc_view": "List",
                    "label": "📋 قائمة مرتجعات المبيعات",
                    "description": "عرض جميع مرتجعات المبيعات"
                }
            },
            {
                "type": "shortcut",
                "data": {
                    "shortcut_name": "Credit Notes",
                    "color": "purple",
                    "format": "{} إشعار ائتمان",
                    "link_to": "Sales Invoice",
                    "type": "DocType",
                    "doc_view": "List",
                    "label": "💳 إشعارات الائتمان",
                    "description": "عرض إشعارات الائتمان"
                }
            }
        ]
        
        workspace.content = json.dumps(content, ensure_ascii=False, indent=2)
        workspace.insert(ignore_permissions=True)
        frappe.db.commit()
        
        print(f"✅ تم إنشاء مساحة عمل مرتجع المبيعات: {workspace_name}")
        print(f"🔗 الرابط الصحيح: http://*************:8000/app/{workspace_name}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء مساحة عمل مرتجع المبيعات: {str(e)}")
        import traceback
        traceback.print_exc()

def update_selling_workspace_correct():
    """تحديث مساحة العمل Selling بالروابط الصحيحة"""
    
    try:
        # الحصول على مساحة العمل Selling
        selling_workspace = frappe.get_doc("Workspace", "Selling")
        
        # تحويل المحتوى الحالي إلى قائمة
        if selling_workspace.content:
            content = json.loads(selling_workspace.content)
        else:
            content = []
        
        # إزالة جميع الروابط القديمة
        content = [item for item in content if not (
            item.get('label', '').startswith('💰') or
            item.get('label', '').startswith('🔄') or
            'فاتورة نقدية' in item.get('label', '') or
            'مرتجع مبيعات' in item.get('label', '') or
            'cash' in item.get('link_to', '').lower() or
            'return' in item.get('link_to', '').lower()
        )]
        
        # إضافة الروابط الصحيحة للمساحات الفرعية
        subworkspace_links = [
            {
                "type": "link",
                "label": "💰 فاتورة نقدية",
                "link_type": "Page",
                "link_to": "cash-invoice",  # رابط مباشر بدون workspaces/
                "dependencies": "",
                "description": "مساحة عمل مخصصة للفواتير النقدية",
                "onboard": 0,
                "is_query_report": 0,
                "icon": "money",
                "hidden": 0
            },
            {
                "type": "link", 
                "label": "🔄 مرتجع مبيعات",
                "link_type": "Page",
                "link_to": "sales-return",  # رابط مباشر بدون workspaces/
                "dependencies": "",
                "description": "مساحة عمل مخصصة لمرتجعات المبيعات",
                "onboard": 0,
                "is_query_report": 0,
                "icon": "return",
                "hidden": 0
            }
        ]
        
        # إضافة الروابط في بداية القائمة
        for link in reversed(subworkspace_links):
            content.insert(0, link)
            print(f"✅ تم إضافة رابط مساحة العمل: {link['label']}")
        
        # تحديث المحتوى
        selling_workspace.content = json.dumps(content, ensure_ascii=False, indent=2)
        selling_workspace.save(ignore_permissions=True)
        frappe.db.commit()
        
        print("✅ تم تحديث مساحة العمل Selling بالروابط الصحيحة")
        
        # طباعة الروابط الصحيحة
        print("\n=== الروابط الصحيحة للاختبار ===")
        print("1. مساحة العمل Selling: http://*************:8000/app/selling")
        print("2. مساحة عمل الفاتورة النقدية: http://*************:8000/app/cash-invoice")
        print("3. مساحة عمل مرتجع المبيعات: http://*************:8000/app/sales-return")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث مساحة العمل Selling: {str(e)}")

if __name__ == "__main__":
    create_correct_workspaces()
