#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import frappe
from frappe import _

def test_cash_invoice_setup():
    """اختبار إعداد الفاتورة النقدية"""
    
    print("=== اختبار إعداد زر الفاتورة النقدية ===")
    
    # 1. التحقق من وجود الحقل المخصص
    print("1. التحقق من وجود الحقل المخصص...")
    if frappe.db.exists("Custom Field", "Sales Invoice-is_cash"):
        field = frappe.get_doc("Custom Field", "Sales Invoice-is_cash")
        print(f"   ✓ الحقل موجود: {field.fieldname}")
        print(f"   ✓ التسمية: {field.label}")
        print(f"   ✓ النوع: {field.fieldtype}")
        print(f"   ✓ في قائمة العرض: {field.in_list_view}")
        print(f"   ✓ في المرشح: {field.in_standard_filter}")
    else:
        print("   ✗ الحقل غير موجود")
    
    # 2. التحقق من وجود Client Script
    print("\n2. التحقق من وجود Client Script...")
    if frappe.db.exists("Client Script", "Cash Invoice Customizations"):
        script = frappe.get_doc("Client Script", "Cash Invoice Customizations")
        print(f"   ✓ النص البرمجي موجود: {script.name}")
        print(f"   ✓ مفعل: {script.enabled}")
        print(f"   ✓ النوع: {script.dt}")
        print(f"   ✓ العرض: {script.view}")
    else:
        print("   ✗ النص البرمجي غير موجود")
    
    # 3. التحقق من وجود Property Setter
    print("\n3. التحقق من وجود Property Setter...")
    if frappe.db.exists("Property Setter", "Sales Invoice-is_cash-bold"):
        prop = frappe.get_doc("Property Setter", "Sales Invoice-is_cash-bold")
        print(f"   ✓ Property Setter موجود: {prop.name}")
        print(f"   ✓ القيمة: {prop.value}")
    else:
        print("   ✗ Property Setter غير موجود")
    
    # 4. التحقق من hooks
    print("\n4. التحقق من hooks...")
    hooks = frappe.get_hooks()
    app_js = hooks.get('app_include_js', [])
    erp_optimizer_js = [f for f in app_js if 'erp_optimizer_ui' in f]
    
    if erp_optimizer_js:
        print(f"   ✓ JavaScript files مسجلة: {len(erp_optimizer_js)}")
        for js_file in erp_optimizer_js:
            print(f"     - {js_file}")
    else:
        print("   ✗ لا توجد JavaScript files مسجلة")
    
    # 5. التحقق من doctype_js
    doctype_js = hooks.get('doctype_js', {})
    if 'Sales Invoice' in doctype_js:
        print(f"   ✓ Sales Invoice JS مسجل: {doctype_js['Sales Invoice']}")
    else:
        print("   ✗ Sales Invoice JS غير مسجل")
    
    # 6. التحقق من وجود الملفات الفعلية
    print("\n5. التحقق من وجود الملفات الفعلية...")
    import os
    
    files_to_check = [
        '/home/<USER>/frappe-bench/apps/erp_optimizer_ui/erp_optimizer_ui/public/js/sales_invoice.js',
        '/home/<USER>/frappe-bench/apps/erp_optimizer_ui/erp_optimizer_ui/public/js/sales_invoice_list.js',
        '/home/<USER>/frappe-bench/apps/erp_optimizer_ui/erp_optimizer_ui/public/css/custom_theme.css',
        '/home/<USER>/frappe-bench/apps/erp_optimizer_ui/erp_optimizer_ui/hooks.py'
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"   ✓ {os.path.basename(file_path)} موجود")
        else:
            print(f"   ✗ {os.path.basename(file_path)} غير موجود")
    
    print("\n=== انتهى الاختبار ===")
    
    return True

if __name__ == "__main__":
    test_cash_invoice_setup()