#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import frappe
import json
import os

def cleanup_old_workspaces():
    """تنظيف مساحات العمل القديمة"""

    old_workspaces = ['فاتورة نقدية', 'مرتجع مبيعات', 'Cash Invoice Workspace', 'Sales Return Workspace']

    for ws_name in old_workspaces:
        if frappe.db.exists('Workspace', ws_name):
            try:
                frappe.delete_doc('Workspace', ws_name, ignore_permissions=True)
                print(f'🗑️ تم حذف مساحة العمل: {ws_name}')
            except Exception as e:
                print(f'⚠️ تعذر حذف مساحة العمل {ws_name}: {str(e)}')

    frappe.db.commit()
    print('✅ تم تنظيف مساحات العمل القديمة')

def create_standard_subworkspaces():
    """إنشاء مساحات العمل الفرعية بالطريقة المعيارية"""

    print("🔄 بدء إنشاء مساحات العمل الفرعية بالطريقة المعيارية...")

    # تنظيف مساحات العمل القديمة
    cleanup_old_workspaces()

    # إنشاء مساحة عمل الفاتورة النقدية
    create_cash_invoice_workspace()

    # إنشاء مساحة عمل مرتجع المبيعات
    create_sales_return_workspace()

    # تحديث مساحة العمل Selling
    update_selling_workspace_links()

    print("✅ تم الانتهاء من إنشاء مساحات العمل الفرعية")

def create_cash_invoice_workspace():
    """إنشاء مساحة عمل الفاتورة النقدية"""
    
    try:
        workspace_name = "Cash Invoice"
        
        # حذف مساحة العمل إذا كانت موجودة
        if frappe.db.exists("Workspace", workspace_name):
            frappe.delete_doc("Workspace", workspace_name, ignore_permissions=True)
            print(f"🗑️ تم حذف مساحة العمل السابقة: {workspace_name}")
        
        # إنشاء مساحة العمل الجديدة
        workspace = frappe.new_doc("Workspace")
        workspace.name = workspace_name
        workspace.label = "فاتورة نقدية"
        workspace.title = "فاتورة نقدية"
        workspace.module = "Selling"
        workspace.parent_page = "Selling"
        workspace.public = 1
        workspace.icon = "money"
        workspace.indicator_color = "green"
        
        # محتوى مساحة العمل
        content = [
            {
                "type": "header",
                "data": {
                    "text": "💰 إدارة الفواتير النقدية",
                    "col": 12
                }
            },
            {
                "type": "spacer"
            },
            {
                "type": "shortcut",
                "data": {
                    "shortcut_name": "New Cash Invoice",
                    "color": "Green",
                    "format": "{} فاتورة نقدية جديدة",
                    "link_to": "Sales Invoice",
                    "type": "DocType",
                    "doc_view": "New",
                    "label": "💰 إنشاء فاتورة نقدية",
                    "description": "إنشاء فاتورة نقدية جديدة"
                }
            },
            {
                "type": "shortcut",
                "data": {
                    "shortcut_name": "Cash Invoices",
                    "color": "Blue",
                    "format": "{} فاتورة نقدية",
                    "link_to": "Sales Invoice",
                    "type": "DocType",
                    "doc_view": "List",
                    "label": "📋 الفواتير النقدية",
                    "description": "عرض جميع الفواتير النقدية"
                }
            },
            {
                "type": "shortcut",
                "data": {
                    "shortcut_name": "POS Profile",
                    "color": "Orange",
                    "format": "{} نقطة بيع",
                    "link_to": "POS Profile",
                    "type": "DocType",
                    "doc_view": "List",
                    "label": "🏪 نقاط البيع",
                    "description": "إدارة نقاط البيع"
                }
            }
        ]
        
        workspace.content = json.dumps(content, ensure_ascii=False, indent=2)
        workspace.insert(ignore_permissions=True)
        frappe.db.commit()
        
        print(f"✅ تم إنشاء مساحة عمل الفاتورة النقدية: {workspace_name}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء مساحة عمل الفاتورة النقدية: {str(e)}")
        import traceback
        traceback.print_exc()

def create_sales_return_workspace():
    """إنشاء مساحة عمل مرتجع المبيعات"""
    
    try:
        workspace_name = "Sales Return"
        
        # حذف مساحة العمل إذا كانت موجودة
        if frappe.db.exists("Workspace", workspace_name):
            frappe.delete_doc("Workspace", workspace_name, ignore_permissions=True)
            print(f"🗑️ تم حذف مساحة العمل السابقة: {workspace_name}")
        
        # إنشاء مساحة العمل الجديدة
        workspace = frappe.new_doc("Workspace")
        workspace.name = workspace_name
        workspace.label = "مرتجع مبيعات"
        workspace.title = "مرتجع مبيعات"
        workspace.module = "Selling"
        workspace.parent_page = "Selling"
        workspace.public = 1
        workspace.icon = "return"
        workspace.indicator_color = "red"
        
        # محتوى مساحة العمل
        content = [
            {
                "type": "header",
                "data": {
                    "text": "🔄 إدارة مرتجعات المبيعات",
                    "col": 12
                }
            },
            {
                "type": "spacer"
            },
            {
                "type": "shortcut",
                "data": {
                    "shortcut_name": "New Sales Return",
                    "color": "Red",
                    "format": "{} مرتجع مبيعات جديد",
                    "link_to": "Sales Invoice",
                    "type": "DocType",
                    "doc_view": "New",
                    "label": "🔄 إنشاء مرتجع مبيعات",
                    "description": "إنشاء مرتجع مبيعات جديد"
                }
            },
            {
                "type": "shortcut",
                "data": {
                    "shortcut_name": "Sales Returns",
                    "color": "Orange",
                    "format": "{} مرتجع مبيعات",
                    "link_to": "Sales Invoice",
                    "type": "DocType",
                    "doc_view": "List",
                    "label": "📋 مرتجعات المبيعات",
                    "description": "عرض جميع مرتجعات المبيعات"
                }
            },
            {
                "type": "shortcut",
                "data": {
                    "shortcut_name": "Credit Notes",
                    "color": "Purple",
                    "format": "{} إشعار ائتمان",
                    "link_to": "Sales Invoice",
                    "type": "DocType",
                    "doc_view": "List",
                    "label": "💳 إشعارات الائتمان",
                    "description": "عرض إشعارات الائتمان"
                }
            }
        ]
        
        workspace.content = json.dumps(content, ensure_ascii=False, indent=2)
        workspace.insert(ignore_permissions=True)
        frappe.db.commit()
        
        print(f"✅ تم إنشاء مساحة عمل مرتجع المبيعات: {workspace_name}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء مساحة عمل مرتجع المبيعات: {str(e)}")
        import traceback
        traceback.print_exc()

def update_selling_workspace_links():
    """تحديث مساحة العمل Selling لتحتوي على روابط المساحات الفرعية"""
    
    try:
        # الحصول على مساحة العمل Selling
        selling_workspace = frappe.get_doc("Workspace", "Selling")
        
        # تحويل المحتوى الحالي إلى قائمة
        if selling_workspace.content:
            content = json.loads(selling_workspace.content)
        else:
            content = []
        
        # إزالة الروابط القديمة
        content = [item for item in content if not (
            item.get('label', '').startswith('💰') or
            item.get('label', '').startswith('🔄') or
            'فاتورة نقدية' in item.get('label', '') or
            'مرتجع مبيعات' in item.get('label', '')
        )]
        
        # إضافة روابط مساحات العمل الفرعية
        subworkspace_links = [
            {
                "type": "link",
                "label": "💰 فاتورة نقدية",
                "link_type": "Page",
                "link_to": "Workspaces/Cash Invoice",
                "dependencies": "",
                "description": "مساحة عمل مخصصة للفواتير النقدية",
                "onboard": 0,
                "is_query_report": 0,
                "icon": "money",
                "hidden": 0
            },
            {
                "type": "link", 
                "label": "🔄 مرتجع مبيعات",
                "link_type": "Page",
                "link_to": "Workspaces/Sales Return",
                "dependencies": "",
                "description": "مساحة عمل مخصصة لمرتجعات المبيعات",
                "onboard": 0,
                "is_query_report": 0,
                "icon": "return",
                "hidden": 0
            }
        ]
        
        # إضافة الروابط في بداية القائمة
        for link in reversed(subworkspace_links):
            content.insert(0, link)
            print(f"✅ تم إضافة رابط مساحة العمل: {link['label']}")
        
        # تحديث المحتوى
        selling_workspace.content = json.dumps(content, ensure_ascii=False, indent=2)
        selling_workspace.save(ignore_permissions=True)
        frappe.db.commit()
        
        print("✅ تم تحديث مساحة العمل Selling بروابط مساحات العمل الفرعية")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث مساحة العمل Selling: {str(e)}")

if __name__ == "__main__":
    create_standard_subworkspaces()
