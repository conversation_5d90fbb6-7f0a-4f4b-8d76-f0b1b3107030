#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import frappe
import json
import os

def install_subworkspaces():
    """تثبيت مساحات العمل الفرعية"""
    
    print("🔄 بدء تثبيت مساحات العمل الفرعية...")
    
    # تثبيت مساحة عمل الفاتورة النقدية
    install_cash_invoice_workspace()
    
    # تثبيت مساحة عمل مرتجع المبيعات
    install_sales_return_workspace()
    
    # تحديث مساحة العمل Selling
    update_selling_workspace()
    
    print("✅ تم الانتهاء من تثبيت مساحات العمل الفرعية")

def install_cash_invoice_workspace():
    """تثبيت مساحة عمل الفاتورة النقدية"""
    
    try:
        workspace_name = "فاتورة نقدية"
        
        if frappe.db.exists("Workspace", workspace_name):
            print(f"⚠️ مساحة العمل '{workspace_name}' موجودة مسبقاً")
            return
        
        # قراءة ملف JSON
        app_path = frappe.get_app_path("erp_optimizer_ui")
        file_path = os.path.join(app_path, "workspace", "فاتورة_نقدية.json")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # إنشاء مساحة العمل
        doc = frappe.new_doc("Workspace")
        doc.name = data["name"]
        doc.label = data["label"]
        doc.module = data["module"]
        doc.public = data.get("public", 1)
        doc.content = json.dumps(data["content"], ensure_ascii=False, indent=2)
        
        doc.insert(ignore_permissions=True)
        frappe.db.commit()
        
        print(f"✅ تم إنشاء مساحة العمل: {workspace_name}")
        
    except Exception as e:
        print(f"❌ خطأ في تثبيت مساحة عمل الفاتورة النقدية: {str(e)}")

def install_sales_return_workspace():
    """تثبيت مساحة عمل مرتجع المبيعات"""
    
    try:
        workspace_name = "مرتجع مبيعات"
        
        if frappe.db.exists("Workspace", workspace_name):
            print(f"⚠️ مساحة العمل '{workspace_name}' موجودة مسبقاً")
            return
        
        # قراءة ملف JSON
        app_path = frappe.get_app_path("erp_optimizer_ui")
        file_path = os.path.join(app_path, "workspace", "مرتجع_مبيعات.json")
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # إنشاء مساحة العمل
        doc = frappe.new_doc("Workspace")
        doc.name = data["name"]
        doc.label = data["label"]
        doc.module = data["module"]
        doc.public = data.get("public", 1)
        doc.content = json.dumps(data["content"], ensure_ascii=False, indent=2)
        
        doc.insert(ignore_permissions=True)
        frappe.db.commit()
        
        print(f"✅ تم إنشاء مساحة العمل: {workspace_name}")
        
    except Exception as e:
        print(f"❌ خطأ في تثبيت مساحة عمل مرتجع المبيعات: {str(e)}")

def update_selling_workspace():
    """تحديث مساحة العمل Selling لتحتوي على روابط مساحات العمل الفرعية"""
    
    try:
        # الحصول على مساحة العمل Selling
        selling_workspace = frappe.get_doc("Workspace", "Selling")
        
        # تحويل المحتوى الحالي إلى قائمة
        if selling_workspace.content:
            content = json.loads(selling_workspace.content)
        else:
            content = []
        
        # إزالة الروابط القديمة للفاتورة النقدية ومرتجع المبيعات
        content = [item for item in content if not (
            item.get('label', '').startswith('💰 إنشاء فاتورة نقدية') or
            item.get('label', '').startswith('🔄 إنشاء مرتجع مبيعات') or
            item.get('label', '').startswith('💰 فاتورة نقدية') or
            item.get('label', '').startswith('🔄 مرتجع مبيعات')
        )]
        
        # إضافة روابط مساحات العمل الفرعية
        subworkspace_links = [
            {
                "type": "link",
                "label": "💰 فاتورة نقدية",
                "link_type": "Page",
                "link_to": "Workspaces/فاتورة نقدية",
                "dependencies": "",
                "description": "مساحة عمل مخصصة للفواتير النقدية",
                "onboard": 0,
                "is_query_report": 0,
                "icon": "money",
                "hidden": 0
            },
            {
                "type": "link", 
                "label": "🔄 مرتجع مبيعات",
                "link_type": "Page",
                "link_to": "Workspaces/مرتجع مبيعات",
                "dependencies": "",
                "description": "مساحة عمل مخصصة لمرتجعات المبيعات",
                "onboard": 0,
                "is_query_report": 0,
                "icon": "return",
                "hidden": 0
            }
        ]
        
        # إضافة الروابط في بداية القائمة
        for link in reversed(subworkspace_links):
            content.insert(0, link)
            print(f"✅ تم إضافة رابط مساحة العمل: {link['label']}")
        
        # تحديث المحتوى
        selling_workspace.content = json.dumps(content, ensure_ascii=False, indent=2)
        selling_workspace.save(ignore_permissions=True)
        frappe.db.commit()
        
        print("✅ تم تحديث مساحة العمل Selling بروابط مساحات العمل الفرعية")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث مساحة العمل Selling: {str(e)}")

if __name__ == "__main__":
    install_subworkspaces()
