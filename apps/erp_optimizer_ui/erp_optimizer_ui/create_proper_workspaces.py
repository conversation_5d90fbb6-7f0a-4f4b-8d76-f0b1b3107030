#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import frappe
import json
import os

def cleanup_and_create_proper_workspaces():
    """حذف مساحات العمل الخاطئة وإنشاء مساحات عمل صحيحة"""
    
    print("🔄 بدء تنظيف وإنشاء مساحات العمل الصحيحة...")
    
    # حذف مساحات العمل الخاطئة
    cleanup_incorrect_workspaces()
    
    # إنشاء مساحات العمل الصحيحة
    create_cash_invoice_workspace()
    create_sales_return_workspace()
    
    # تحديث مساحة العمل Selling
    update_selling_workspace()
    
    print("✅ تم الانتهاء من إنشاء مساحات العمل الصحيحة")

def cleanup_incorrect_workspaces():
    """حذف مساحات العمل الخاطئة"""
    
    incorrect_workspaces = [
        'فاتورة نقدية', 'مرتجع مبيعات', 
        '💰 فاتورة نقدية', '🔄 مرتجع مبيعات',
        'Cash Invoice', 'Sales Return',
        'Cash Invoice Workspace', 'Sales Return Workspace'
    ]
    
    for ws_name in incorrect_workspaces:
        if frappe.db.exists('Workspace', ws_name):
            try:
                frappe.delete_doc('Workspace', ws_name, ignore_permissions=True)
                print(f'🗑️ تم حذف مساحة العمل الخاطئة: {ws_name}')
            except Exception as e:
                print(f'⚠️ تعذر حذف مساحة العمل {ws_name}: {str(e)}')
    
    frappe.db.commit()
    print('✅ تم تنظيف مساحات العمل الخاطئة')

def create_cash_invoice_workspace():
    """إنشاء مساحة عمل الفاتورة النقدية بالطريقة الصحيحة"""
    
    try:
        workspace_name = "Cash Invoice Management"
        
        if frappe.db.exists("Workspace", workspace_name):
            print(f"⚠️ مساحة العمل {workspace_name} موجودة مسبقاً")
            return
        
        # إنشاء مساحة العمل
        workspace = frappe.new_doc("Workspace")
        workspace.name = workspace_name
        workspace.label = "فاتورة نقدية"
        workspace.title = "فاتورة نقدية"
        workspace.module = "Selling"
        workspace.parent_page = "Selling"
        workspace.public = 1
        workspace.icon = "money"
        workspace.indicator_color = "green"
        
        # محتوى مساحة العمل
        content = [
            {
                "type": "header",
                "data": {
                    "text": "💰 إدارة الفواتير النقدية",
                    "col": 12
                }
            },
            {
                "type": "spacer"
            },
            {
                "type": "shortcut",
                "data": {
                    "shortcut_name": "New Cash Invoice",
                    "color": "green",
                    "format": "{} فاتورة نقدية جديدة",
                    "link_to": "Sales Invoice",
                    "type": "DocType",
                    "doc_view": "New",
                    "label": "💰 إنشاء فاتورة نقدية",
                    "description": "إنشاء فاتورة نقدية جديدة مع تعيين القيم تلقائياً"
                }
            },
            {
                "type": "shortcut",
                "data": {
                    "shortcut_name": "Cash Invoices List",
                    "color": "blue",
                    "format": "{} فاتورة نقدية",
                    "link_to": "Sales Invoice",
                    "type": "DocType",
                    "doc_view": "List",
                    "label": "📋 قائمة الفواتير النقدية",
                    "description": "عرض جميع الفواتير النقدية"
                }
            },
            {
                "type": "shortcut",
                "data": {
                    "shortcut_name": "POS Profile",
                    "color": "orange",
                    "format": "{} نقطة بيع",
                    "link_to": "POS Profile",
                    "type": "DocType",
                    "doc_view": "List",
                    "label": "🏪 نقاط البيع",
                    "description": "إدارة نقاط البيع"
                }
            },
            {
                "type": "card",
                "data": {
                    "card_name": "التقارير النقدية",
                    "col": 6,
                    "links": [
                        {
                            "label": "📊 تقرير المبيعات النقدية",
                            "type": "report",
                            "name": "Sales Register",
                            "description": "تقرير شامل للمبيعات النقدية"
                        },
                        {
                            "label": "💹 تحليل المبيعات النقدية",
                            "type": "report",
                            "name": "Sales Analytics",
                            "description": "تحليل بيانات المبيعات النقدية"
                        }
                    ]
                }
            },
            {
                "type": "card",
                "data": {
                    "card_name": "الإعدادات",
                    "col": 6,
                    "links": [
                        {
                            "label": "💳 طرق الدفع",
                            "type": "doctype",
                            "name": "Mode of Payment",
                            "description": "إدارة طرق الدفع النقدية"
                        },
                        {
                            "label": "🏦 الحسابات النقدية",
                            "type": "doctype",
                            "name": "Account",
                            "description": "إدارة الحسابات النقدية"
                        }
                    ]
                }
            }
        ]
        
        workspace.content = json.dumps(content, ensure_ascii=False, indent=2)
        workspace.insert(ignore_permissions=True)
        frappe.db.commit()
        
        print(f"✅ تم إنشاء مساحة عمل الفاتورة النقدية: {workspace_name}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء مساحة عمل الفاتورة النقدية: {str(e)}")
        import traceback
        traceback.print_exc()

def create_sales_return_workspace():
    """إنشاء مساحة عمل مرتجع المبيعات بالطريقة الصحيحة"""
    
    try:
        workspace_name = "Sales Return Management"
        
        if frappe.db.exists("Workspace", workspace_name):
            print(f"⚠️ مساحة العمل {workspace_name} موجودة مسبقاً")
            return
        
        # إنشاء مساحة العمل
        workspace = frappe.new_doc("Workspace")
        workspace.name = workspace_name
        workspace.label = "مرتجع مبيعات"
        workspace.title = "مرتجع مبيعات"
        workspace.module = "Selling"
        workspace.parent_page = "Selling"
        workspace.public = 1
        workspace.icon = "return"
        workspace.indicator_color = "red"
        
        # محتوى مساحة العمل
        content = [
            {
                "type": "header",
                "data": {
                    "text": "🔄 إدارة مرتجعات المبيعات",
                    "col": 12
                }
            },
            {
                "type": "spacer"
            },
            {
                "type": "shortcut",
                "data": {
                    "shortcut_name": "New Sales Return",
                    "color": "red",
                    "format": "{} مرتجع مبيعات جديد",
                    "link_to": "Sales Invoice",
                    "type": "DocType",
                    "doc_view": "New",
                    "label": "🔄 إنشاء مرتجع مبيعات",
                    "description": "إنشاء مرتجع مبيعات جديد مع تعيين القيم تلقائياً"
                }
            },
            {
                "type": "shortcut",
                "data": {
                    "shortcut_name": "Sales Returns List",
                    "color": "orange",
                    "format": "{} مرتجع مبيعات",
                    "link_to": "Sales Invoice",
                    "type": "DocType",
                    "doc_view": "List",
                    "label": "📋 قائمة مرتجعات المبيعات",
                    "description": "عرض جميع مرتجعات المبيعات"
                }
            },
            {
                "type": "shortcut",
                "data": {
                    "shortcut_name": "Credit Notes",
                    "color": "purple",
                    "format": "{} إشعار ائتمان",
                    "link_to": "Sales Invoice",
                    "type": "DocType",
                    "doc_view": "List",
                    "label": "💳 إشعارات الائتمان",
                    "description": "عرض إشعارات الائتمان"
                }
            },
            {
                "type": "card",
                "data": {
                    "card_name": "تقارير المرتجعات",
                    "col": 6,
                    "links": [
                        {
                            "label": "📊 تقرير مرتجعات المبيعات",
                            "type": "report",
                            "name": "Sales Register",
                            "description": "تقرير شامل لمرتجعات المبيعات"
                        },
                        {
                            "label": "💹 تحليل المرتجعات",
                            "type": "report",
                            "name": "Sales Analytics",
                            "description": "تحليل بيانات مرتجعات المبيعات"
                        }
                    ]
                }
            },
            {
                "type": "card",
                "data": {
                    "card_name": "إدارة المخزون",
                    "col": 6,
                    "links": [
                        {
                            "label": "📦 إرجاع الأصناف",
                            "type": "doctype",
                            "name": "Stock Entry",
                            "description": "إدارة إرجاع الأصناف للمخزون"
                        },
                        {
                            "label": "🏪 مخازن الإرجاع",
                            "type": "doctype",
                            "name": "Warehouse",
                            "description": "إدارة مخازن الإرجاع"
                        }
                    ]
                }
            }
        ]
        
        workspace.content = json.dumps(content, ensure_ascii=False, indent=2)
        workspace.insert(ignore_permissions=True)
        frappe.db.commit()
        
        print(f"✅ تم إنشاء مساحة عمل مرتجع المبيعات: {workspace_name}")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء مساحة عمل مرتجع المبيعات: {str(e)}")
        import traceback
        traceback.print_exc()

def update_selling_workspace():
    """تحديث مساحة العمل Selling لتحتوي على روابط صحيحة للمساحات الفرعية"""
    
    try:
        # الحصول على مساحة العمل Selling
        selling_workspace = frappe.get_doc("Workspace", "Selling")
        
        # تحويل المحتوى الحالي إلى قائمة
        if selling_workspace.content:
            content = json.loads(selling_workspace.content)
        else:
            content = []
        
        # إزالة جميع الروابط القديمة
        content = [item for item in content if not (
            item.get('label', '').startswith('💰') or
            item.get('label', '').startswith('🔄') or
            'فاتورة نقدية' in item.get('label', '') or
            'مرتجع مبيعات' in item.get('label', '') or
            'Cash Invoice' in item.get('link_to', '') or
            'Sales Return' in item.get('link_to', '')
        )]
        
        # إضافة الروابط الصحيحة للمساحات الفرعية
        subworkspace_links = [
            {
                "type": "link",
                "label": "💰 فاتورة نقدية",
                "link_type": "Page",
                "link_to": "Workspaces/Cash Invoice Management",
                "dependencies": "",
                "description": "مساحة عمل مخصصة للفواتير النقدية",
                "onboard": 0,
                "is_query_report": 0,
                "icon": "money",
                "hidden": 0
            },
            {
                "type": "link", 
                "label": "🔄 مرتجع مبيعات",
                "link_type": "Page",
                "link_to": "Workspaces/Sales Return Management",
                "dependencies": "",
                "description": "مساحة عمل مخصصة لمرتجعات المبيعات",
                "onboard": 0,
                "is_query_report": 0,
                "icon": "return",
                "hidden": 0
            }
        ]
        
        # إضافة الروابط في بداية القائمة
        for link in reversed(subworkspace_links):
            content.insert(0, link)
            print(f"✅ تم إضافة رابط مساحة العمل: {link['label']}")
        
        # تحديث المحتوى
        selling_workspace.content = json.dumps(content, ensure_ascii=False, indent=2)
        selling_workspace.save(ignore_permissions=True)
        frappe.db.commit()
        
        print("✅ تم تحديث مساحة العمل Selling بالروابط الصحيحة")
        
        # طباعة الروابط الصحيحة
        print("\n=== الروابط الصحيحة للاختبار ===")
        print("1. مساحة العمل Selling: http://192.168.8.157:8000/app/workspaces/Selling")
        print("2. مساحة عمل الفاتورة النقدية: http://192.168.8.157:8000/app/workspaces/Cash%20Invoice%20Management")
        print("3. مساحة عمل مرتجع المبيعات: http://192.168.8.157:8000/app/workspaces/Sales%20Return%20Management")
        
    except Exception as e:
        print(f"❌ خطأ في تحديث مساحة العمل Selling: {str(e)}")

if __name__ == "__main__":
    cleanup_and_create_proper_workspaces()
