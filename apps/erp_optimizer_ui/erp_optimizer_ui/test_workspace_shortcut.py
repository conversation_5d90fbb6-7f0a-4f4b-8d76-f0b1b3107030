#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import frappe
from frappe import _
import json

def test_workspace_shortcut():
    """اختبار اختصار الفاتورة النقدية في مساحة العمل"""
    
    print("=== اختبار اختصار الفاتورة النقدية في مساحة العمل ===")
    
    # 1. التحقق من وجود مساحة العمل
    print("1. التحقق من وجود مساحة العمل...")
    if frappe.db.exists("Workspace", "المبيعات"):
        workspace = frappe.get_doc("Workspace", "المبيعات")
        print(f"   ✓ مساحة العمل موجودة: {workspace.name}")
        print(f"   ✓ التسمية: {workspace.label}")
        print(f"   ✓ الوحدة: {workspace.module}")
        print(f"   ✓ عامة: {workspace.public}")
        
        # التحقق من المحتوى
        if workspace.content:
            try:
                content = json.loads(workspace.content)
                print(f"   ✓ عدد العناصر: {len(content)}")
                
                # البحث عن اختصار الفاتورة النقدية
                cash_invoice_shortcut = None
                for item in content:
                    if item.get('label') == '💰 إنشاء فاتورة نقدية':
                        cash_invoice_shortcut = item
                        break
                
                if cash_invoice_shortcut:
                    print("   ✓ اختصار الفاتورة النقدية موجود:")
                    print(f"     - التسمية: {cash_invoice_shortcut.get('label')}")
                    print(f"     - النوع: {cash_invoice_shortcut.get('type')}")
                    print(f"     - نوع الرابط: {cash_invoice_shortcut.get('link_type')}")
                    print(f"     - الرابط إلى: {cash_invoice_shortcut.get('link_to')}")
                    print(f"     - الوصف: {cash_invoice_shortcut.get('description')}")
                else:
                    print("   ✗ اختصار الفاتورة النقدية غير موجود")
                    
            except Exception as e:
                print(f"   ✗ خطأ في قراءة المحتوى: {str(e)}")
        else:
            print("   ✗ لا توجد محتويات في مساحة العمل")
    else:
        print("   ✗ مساحة العمل غير موجودة")
    
    # 2. التحقق من وجود ملف workspace_customization.js
    print("\n2. التحقق من وجود ملف التخصيص...")
    import os
    
    js_path = '/home/<USER>/frappe-bench/apps/erp_optimizer_ui/erp_optimizer_ui/public/js/workspace_customization.js'
    if os.path.exists(js_path):
        print("   ✓ ملف workspace_customization.js موجود")
        
        # التحقق من وجود الدوال المطلوبة
        with open(js_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
            if 'setup_cash_invoice_workspace_shortcut' in content:
                print("   ✓ دالة setup_cash_invoice_workspace_shortcut موجودة")
            else:
                print("   ✗ دالة setup_cash_invoice_workspace_shortcut غير موجودة")
                
            if 'create_cash_invoice_from_workspace' in content:
                print("   ✓ دالة create_cash_invoice_from_workspace موجودة")
            else:
                print("   ✗ دالة create_cash_invoice_from_workspace غير موجودة")
                
            if '💰 إنشاء فاتورة نقدية' in content:
                print("   ✓ نص الاختصار موجود في الكود")
            else:
                print("   ✗ نص الاختصار غير موجود في الكود")
    else:
        print("   ✗ ملف workspace_customization.js غير موجود")
    
    # 3. التحقق من hooks
    print("\n3. التحقق من hooks...")
    hooks = frappe.get_hooks()
    app_js = hooks.get('app_include_js', [])
    workspace_js = [f for f in app_js if 'workspace_customization' in f]
    
    if workspace_js:
        print(f"   ✓ ملف workspace_customization.js مسجل في hooks")
        for js_file in workspace_js:
            print(f"     - {js_file}")
    else:
        print("   ✗ ملف workspace_customization.js غير مسجل في hooks")
    
    print("\n=== ملخص النتائج ===")
    print("✓ اختصار '💰 إنشاء فاتورة نقدية' يجب أن يظهر في أعلى مساحة العمل للمبيعات")
    print("✓ عند النقر على الاختصار يتم إنشاء فاتورة نقدية جديدة")
    print("✓ الفاتورة الجديدة تحتوي على حقل 'فاتورة نقدية؟' مفعل")
    
    print("\n=== تعليمات الاختبار ===")
    print("1. اذهب إلى مساحة العمل للمبيعات")
    print("2. ابحث عن اختصار '💰 إنشاء فاتورة نقدية' في أعلى القائمة")
    print("3. انقر على الاختصار")
    print("4. تأكد من فتح فاتورة جديدة مع تفعيل حقل 'فاتورة نقدية؟'")
    print("5. تأكد من ظهور رسالة تأكيد 'تم إنشاء فاتورة نقدية جديدة'")
    
    return True

if __name__ == "__main__":
    test_workspace_shortcut()