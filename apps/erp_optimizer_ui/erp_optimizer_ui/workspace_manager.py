#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import frappe
import json

def get_selling_workspace():
    """الحصول على معلومات مساحة العمل Selling"""
    
    try:
        # البحث عن مساحة العمل Selling
        selling_workspace = frappe.get_doc("Workspace", "Selling")
        
        print("=== معلومات مساحة العمل Selling ===")
        print(f"الاسم: {selling_workspace.name}")
        print(f"التسمية: {selling_workspace.label}")
        print(f"الوحدة: {selling_workspace.module}")
        print(f"عامة: {selling_workspace.public}")
        print(f"قياسية: {getattr(selling_workspace, 'is_standard', 'غير محدد')}")
        
        # طباعة المحتوى
        if selling_workspace.content:
            content = json.loads(selling_workspace.content)
            print("\n=== المحتوى الحالي ===")
            for i, item in enumerate(content, 1):
                print(f"{i}. {item.get('label', 'بدون تسمية')} - {item.get('type', 'نوع غير محدد')}")
                if item.get('link_to'):
                    print(f"   الرابط: {item.get('link_to')}")
                if item.get('description'):
                    print(f"   الوصف: {item.get('description')}")
                print()
        
        return selling_workspace
        
    except frappe.DoesNotExistError:
        print("❌ مساحة العمل Selling غير موجودة")
        return None
    except Exception as e:
        print(f"❌ خطأ في الحصول على مساحة العمل: {str(e)}")
        return None

def delete_custom_sales_workspace():
    """حذف مساحة العمل المخصصة 'المبيعات'"""
    
    try:
        if frappe.db.exists("Workspace", "المبيعات"):
            frappe.delete_doc("Workspace", "المبيعات")
            frappe.db.commit()
            print("✅ تم حذف مساحة العمل المخصصة 'المبيعات'")
        else:
            print("⚠️ مساحة العمل 'المبيعات' غير موجودة")
    except Exception as e:
        print(f"❌ خطأ في حذف مساحة العمل: {str(e)}")

def add_customizations_to_selling_workspace():
    """إضافة التخصيصات إلى مساحة العمل Selling الأصلية"""
    
    try:
        # الحصول على مساحة العمل Selling
        selling_workspace = frappe.get_doc("Workspace", "Selling")
        
        # تحويل المحتوى الحالي إلى قائمة
        if selling_workspace.content:
            content = json.loads(selling_workspace.content)
        else:
            content = []
        
        # التخصيصات الجديدة المراد إضافتها
        new_customizations = [
            {
                "type": "link",
                "label": "💰 إنشاء فاتورة نقدية",
                "link_type": "DocType",
                "link_to": "Sales Invoice",
                "dependencies": "",
                "description": "إنشاء فاتورة نقدية جديدة مع تعيين القيم المطلوبة تلقائياً",
                "onboard": 0,
                "is_query_report": 0,
                "icon": "money",
                "hidden": 0
            },
            {
                "type": "link", 
                "label": "🔄 إنشاء مرتجع مبيعات",
                "link_type": "DocType",
                "link_to": "Sales Invoice",
                "dependencies": "",
                "description": "إنشاء مرتجع مبيعات جديد",
                "onboard": 0,
                "is_query_report": 0,
                "icon": "return",
                "hidden": 0
            }
        ]
        
        # التحقق من عدم وجود التخصيصات مسبقاً
        existing_labels = [item.get('label', '') for item in content]
        
        for customization in new_customizations:
            if customization['label'] not in existing_labels:
                # إضافة التخصيص في بداية القائمة
                content.insert(0, customization)
                print(f"✅ تم إضافة: {customization['label']}")
            else:
                print(f"⚠️ موجود مسبقاً: {customization['label']}")
        
        # تحديث المحتوى
        selling_workspace.content = json.dumps(content, ensure_ascii=False, indent=2)
        selling_workspace.save(ignore_permissions=True)
        frappe.db.commit()
        
        print("✅ تم تحديث مساحة العمل Selling بالتخصيصات الجديدة")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة التخصيصات: {str(e)}")

def migrate_sales_workspace():
    """نقل التخصيصات من مساحة العمل المخصصة إلى الأصلية وحذف المخصصة"""
    
    print("🔄 بدء عملية نقل التخصيصات...")
    
    # 1. إضافة التخصيصات إلى مساحة العمل الأصلية
    add_customizations_to_selling_workspace()
    
    # 2. حذف مساحة العمل المخصصة
    delete_custom_sales_workspace()
    
    print("✅ تم الانتهاء من عملية النقل")

def list_all_workspaces():
    """عرض جميع مساحات العمل"""
    
    workspaces = frappe.get_all("Workspace", fields=["name", "label", "module", "public", "is_standard"])
    
    print("\n=== جميع مساحات العمل ===")
    for ws in workspaces:
        print(f"- {ws.name} ({ws.label}) - الوحدة: {ws.module} - عامة: {ws.public} - قياسية: {ws.is_standard}")

def update_selling_workspace_with_subworkspaces():
    """تحديث مساحة العمل Selling لتحتوي على روابط لمساحات العمل الفرعية"""

    try:
        # الحصول على مساحة العمل Selling
        selling_workspace = frappe.get_doc("Workspace", "Selling")

        # تحويل المحتوى الحالي إلى قائمة
        if selling_workspace.content:
            content = json.loads(selling_workspace.content)
        else:
            content = []

        # إزالة الروابط القديمة للفاتورة النقدية ومرتجع المبيعات
        content = [item for item in content if not (
            item.get('label', '').startswith('💰 إنشاء فاتورة نقدية') or
            item.get('label', '').startswith('🔄 إنشاء مرتجع مبيعات')
        )]

        # إضافة روابط مساحات العمل الفرعية
        subworkspace_links = [
            {
                "type": "link",
                "label": "💰 فاتورة نقدية",
                "link_type": "Page",
                "link_to": "Workspaces/فاتورة نقدية",
                "dependencies": "",
                "description": "مساحة عمل مخصصة للفواتير النقدية",
                "onboard": 0,
                "is_query_report": 0,
                "icon": "money",
                "hidden": 0
            },
            {
                "type": "link",
                "label": "🔄 مرتجع مبيعات",
                "link_type": "Page",
                "link_to": "Workspaces/مرتجع مبيعات",
                "dependencies": "",
                "description": "مساحة عمل مخصصة لمرتجعات المبيعات",
                "onboard": 0,
                "is_query_report": 0,
                "icon": "return",
                "hidden": 0
            }
        ]

        # إضافة الروابط في بداية القائمة
        for link in reversed(subworkspace_links):
            content.insert(0, link)
            print(f"✅ تم إضافة رابط مساحة العمل: {link['label']}")

        # تحديث المحتوى
        selling_workspace.content = json.dumps(content, ensure_ascii=False, indent=2)
        selling_workspace.save(ignore_permissions=True)
        frappe.db.commit()

        print("✅ تم تحديث مساحة العمل Selling بروابط مساحات العمل الفرعية")

    except Exception as e:
        print(f"❌ خطأ في تحديث مساحة العمل: {str(e)}")

def create_subworkspaces_with_parent():
    """إنشاء مساحات العمل الفرعية مع تحديد مساحة العمل الأب"""

    try:
        print("🔄 بدء إنشاء مساحات العمل الفرعية...")

        # إنشاء مساحة عمل الفاتورة النقدية
        cash_workspace_name = "Cash Invoice Workspace"
        if not frappe.db.exists('Workspace', cash_workspace_name):
            cash_doc = frappe.new_doc('Workspace')
            cash_doc.name = cash_workspace_name
            cash_doc.label = "💰 فاتورة نقدية"
            cash_doc.title = "فاتورة نقدية"  # إضافة حقل title
            cash_doc.module = 'Selling'
            cash_doc.parent_page = 'Selling'  # تحديد مساحة العمل الأب
            cash_doc.public = 1
            cash_doc.icon = 'money'

            # محتوى مساحة عمل الفاتورة النقدية
            cash_content = [
                {
                    "type": "shortcut",
                    "data": {
                        "shortcut_name": "إنشاء فاتورة نقدية",
                        "color": "Green",
                        "format": "{} فاتورة نقدية جديدة",
                        "link_to": "Sales Invoice",
                        "type": "DocType",
                        "doc_view": "New",
                        "label": "💰 إنشاء فاتورة نقدية",
                        "description": "إنشاء فاتورة نقدية جديدة"
                    }
                },
                {
                    "type": "shortcut",
                    "data": {
                        "shortcut_name": "الفواتير النقدية",
                        "color": "Blue",
                        "format": "{} فاتورة نقدية",
                        "link_to": "Sales Invoice",
                        "type": "DocType",
                        "doc_view": "List",
                        "label": "📋 الفواتير النقدية",
                        "description": "عرض جميع الفواتير النقدية"
                    }
                }
            ]

            cash_doc.content = json.dumps(cash_content, ensure_ascii=False, indent=2)
            cash_doc.insert(ignore_permissions=True)
            print(f'✅ تم إنشاء مساحة عمل الفاتورة النقدية: {cash_workspace_name}')
        else:
            print(f'⚠️ مساحة عمل الفاتورة النقدية موجودة مسبقاً: {cash_workspace_name}')

        # إنشاء مساحة عمل مرتجع المبيعات
        return_workspace_name = "Sales Return Workspace"
        if not frappe.db.exists('Workspace', return_workspace_name):
            return_doc = frappe.new_doc('Workspace')
            return_doc.name = return_workspace_name
            return_doc.label = "🔄 مرتجع مبيعات"
            return_doc.title = "مرتجع مبيعات"  # إضافة حقل title
            return_doc.module = 'Selling'
            return_doc.parent_page = 'Selling'  # تحديد مساحة العمل الأب
            return_doc.public = 1
            return_doc.icon = 'return'

            # محتوى مساحة عمل مرتجع المبيعات
            return_content = [
                {
                    "type": "shortcut",
                    "data": {
                        "shortcut_name": "إنشاء مرتجع مبيعات",
                        "color": "Red",
                        "format": "{} مرتجع مبيعات جديد",
                        "link_to": "Sales Invoice",
                        "type": "DocType",
                        "doc_view": "New",
                        "label": "🔄 إنشاء مرتجع مبيعات",
                        "description": "إنشاء مرتجع مبيعات جديد"
                    }
                },
                {
                    "type": "shortcut",
                    "data": {
                        "shortcut_name": "مرتجعات المبيعات",
                        "color": "Orange",
                        "format": "{} مرتجع مبيعات",
                        "link_to": "Sales Invoice",
                        "type": "DocType",
                        "doc_view": "List",
                        "label": "📋 مرتجعات المبيعات",
                        "description": "عرض جميع مرتجعات المبيعات"
                    }
                }
            ]

            return_doc.content = json.dumps(return_content, ensure_ascii=False, indent=2)
            return_doc.insert(ignore_permissions=True)
            print(f'✅ تم إنشاء مساحة عمل مرتجع المبيعات: {return_workspace_name}')
        else:
            print(f'⚠️ مساحة عمل مرتجع المبيعات موجودة مسبقاً: {return_workspace_name}')

        frappe.db.commit()

        # تحديث مساحة العمل Selling لتحتوي على روابط المساحات الفرعية
        update_selling_with_child_workspaces(cash_workspace_name, return_workspace_name)

        print("✅ تم الانتهاء من إنشاء مساحات العمل الفرعية")

    except Exception as e:
        print(f"❌ خطأ في إنشاء مساحات العمل الفرعية: {str(e)}")
        import traceback
        traceback.print_exc()

def update_selling_with_child_workspaces(cash_workspace_name, return_workspace_name):
    """تحديث مساحة العمل Selling لتحتوي على روابط المساحات الفرعية"""

    try:
        # الحصول على مساحة العمل Selling
        selling_workspace = frappe.get_doc("Workspace", "Selling")

        # تحويل المحتوى الحالي إلى قائمة
        if selling_workspace.content:
            content = json.loads(selling_workspace.content)
        else:
            content = []

        # إزالة الروابط القديمة
        content = [item for item in content if not (
            item.get('label', '').startswith('💰') or
            item.get('label', '').startswith('🔄') or
            'فاتورة نقدية' in item.get('label', '') or
            'مرتجع مبيعات' in item.get('label', '')
        )]

        # إضافة روابط مساحات العمل الفرعية
        subworkspace_links = [
            {
                "type": "link",
                "label": "💰 فاتورة نقدية",
                "link_type": "Page",
                "link_to": f"Workspaces/{cash_workspace_name}",
                "dependencies": "",
                "description": "مساحة عمل مخصصة للفواتير النقدية",
                "onboard": 0,
                "is_query_report": 0,
                "icon": "money",
                "hidden": 0
            },
            {
                "type": "link",
                "label": "🔄 مرتجع مبيعات",
                "link_type": "Page",
                "link_to": f"Workspaces/{return_workspace_name}",
                "dependencies": "",
                "description": "مساحة عمل مخصصة لمرتجعات المبيعات",
                "onboard": 0,
                "is_query_report": 0,
                "icon": "return",
                "hidden": 0
            }
        ]

        # إضافة الروابط في بداية القائمة
        for link in reversed(subworkspace_links):
            content.insert(0, link)
            print(f"✅ تم إضافة رابط مساحة العمل: {link['label']}")

        # تحديث المحتوى
        selling_workspace.content = json.dumps(content, ensure_ascii=False, indent=2)
        selling_workspace.save(ignore_permissions=True)
        frappe.db.commit()

        print("✅ تم تحديث مساحة العمل Selling بروابط مساحات العمل الفرعية")

    except Exception as e:
        print(f"❌ خطأ في تحديث مساحة العمل Selling: {str(e)}")

if __name__ == "__main__":
    get_selling_workspace()
    list_all_workspaces()
