#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import frappe
import urllib.parse

def check_workspace_urls():
    """التحقق من روابط مساحات العمل"""
    
    try:
        print("🔍 التحقق من روابط مساحات العمل...")
        
        # البحث عن مساحات العمل المرتبطة بالفواتير النقدية والمرتجعات
        workspaces = frappe.get_all('Workspace', 
                                   fields=['name', 'label', 'title', 'parent_page'],
                                   order_by='name')
        
        print("\n=== جميع مساحات العمل ===")
        for ws in workspaces:
            # التحقق من مساحات العمل المرتبطة بموضوعنا
            if any(keyword in ws.name.lower() or keyword in (ws.label or '').lower() 
                   for keyword in ['cash', 'invoice', 'return', 'فاتورة', 'نقدية', 'مرتجع', 'selling']):
                
                print(f"📋 Name: {ws.name}")
                print(f"   Label: {ws.label}")
                print(f"   Title: {ws.title}")
                print(f"   Parent: {ws.parent_page or 'None'}")
                print(f"   URL: http://192.168.8.157:8000/app/{ws.name}")
                
                # إذا كان الاسم يحتوي على أحرف عربية، اعرض النسخة المُرمزة
                if any(ord(char) > 127 for char in ws.name):
                    encoded_name = urllib.parse.quote(ws.name)
                    print(f"   URL (encoded): http://192.168.8.157:8000/app/{encoded_name}")
                
                print("   ---")
        
        # التحقق من محتوى مساحة العمل Selling
        print("\n=== محتوى مساحة العمل Selling ===")
        selling_workspace = frappe.get_doc("Workspace", "Selling")
        if selling_workspace.content:
            import json
            content = json.loads(selling_workspace.content)
            
            for item in content[:10]:  # أول 10 عناصر فقط
                if item.get('type') == 'link':
                    print(f"🔗 Link: {item.get('label')} -> {item.get('link_to')}")
        
    except Exception as e:
        print(f"❌ خطأ في التحقق من الروابط: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_workspace_urls()
