#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import frappe
from frappe import _
import json

def install_sales_workspace():
    """تثبيت مساحة العمل للمبيعات مع اختصار الفاتورة النقدية"""
    
    workspace_name = "المبيعات"
    
    # محتوى مساحة العمل
    workspace_content = [
        {
            "type": "link",
            "label": "💰 إنشاء فاتورة نقدية",
            "link_type": "DocType",
            "link_to": "Sales Invoice",
            "dependencies": "",
            "description": "إنشاء فاتورة نقدية جديدة مع تعيين القيم المطلوبة تلقائياً"
        },
        {
            "type": "link",
            "label": "فواتير المبيعات",
            "link_type": "DocType",
            "link_to": "Sales Invoice"
        },
        {
            "type": "link",
            "label": "طلبات المبيعات",
            "link_type": "DocType",
            "link_to": "Sales Order"
        },
        {
            "type": "link",
            "label": "العملاء",
            "link_type": "DocType",
            "link_to": "Customer"
        },
        {
            "type": "report",
            "label": "تقرير الفواتير",
            "link_type": "Report",
            "link_to": "Sales Invoice"
        }
    ]
    
    # التحقق من وجود مساحة العمل
    if frappe.db.exists("Workspace", workspace_name):
        # تحديث مساحة العمل الموجودة
        doc = frappe.get_doc("Workspace", workspace_name)
        print(f"تحديث مساحة العمل الموجودة: {workspace_name}")
    else:
        # إنشاء مساحة عمل جديدة
        doc = frappe.new_doc("Workspace")
        doc.name = workspace_name
        print(f"إنشاء مساحة عمل جديدة: {workspace_name}")
    
    # تعيين البيانات
    doc.label = "المبيعات"
    doc.title = "المبيعات"
    doc.module = "erp_optimizer_ui"
    doc.public = 1
    doc.is_standard = 0
    doc.icon = "selling"
    doc.idx = 10
    doc.content = json.dumps(workspace_content, ensure_ascii=False, indent=2)
    
    # حفظ المستند
    if doc.is_new():
        doc.insert(ignore_permissions=True)
        print("✅ تم إنشاء مساحة العمل بنجاح")
    else:
        doc.save(ignore_permissions=True)
        print("✅ تم تحديث مساحة العمل بنجاح")
    
    frappe.db.commit()
    
    # التحقق من النتيجة
    print(f"\n=== تفاصيل مساحة العمل ===")
    print(f"الاسم: {doc.name}")
    print(f"التسمية: {doc.label}")
    print(f"الوحدة: {doc.module}")
    print(f"عامة: {doc.public}")
    print(f"عدد العناصر: {len(workspace_content)}")
    
    print(f"\n=== محتوى مساحة العمل ===")
    for i, item in enumerate(workspace_content, 1):
        print(f"{i}. {item['label']} ({item['type']})")
    
    print(f"\n=== تعليمات ===")
    print("1. اذهب إلى صفحة مساحة العمل للمبيعات")
    print("2. ابحث عن اختصار '💰 إنشاء فاتورة نقدية' في أعلى القائمة")
    print("3. انقر على الاختصار لإنشاء فاتورة نقدية جديدة")
    
    return True

if __name__ == "__main__":
    install_sales_workspace()