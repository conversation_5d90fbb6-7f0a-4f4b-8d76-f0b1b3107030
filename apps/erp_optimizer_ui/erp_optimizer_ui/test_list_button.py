#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import frappe
from frappe import _

def test_list_button_setup():
    """اختبار إعداد زر الفاتورة النقدية في قائمة العرض فقط"""
    
    print("=== اختبار زر الفاتورة النقدية في قائمة العرض ===")
    
    # 1. التحقق من وجود الحقل المخصص
    print("1. التحقق من وجود الحقل المخصص is_cash...")
    if frappe.db.exists("Custom Field", "Sales Invoice-is_cash"):
        field = frappe.get_doc("Custom Field", "Sales Invoice-is_cash")
        print(f"   ✓ الحقل موجود: {field.fieldname}")
        print(f"   ✓ التسمية: {field.label}")
    else:
        print("   ✗ الحقل غير موجود")
    
    # 2. التحقق من وجود ملف sales_invoice_list.js
    print("\n2. التحقق من وجود ملفات JavaScript...")
    import os
    
    list_js_path = '/home/<USER>/frappe-bench/apps/erp_optimizer_ui/erp_optimizer_ui/public/js/sales_invoice_list.js'
    form_js_path = '/home/<USER>/frappe-bench/apps/erp_optimizer_ui/erp_optimizer_ui/public/js/sales_invoice.js'
    
    if os.path.exists(list_js_path):
        print("   ✓ sales_invoice_list.js موجود")
        
        # قراءة محتوى الملف للتحقق من وجود الدالة
        with open(list_js_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if 'add_cash_invoice_button_to_toolbar' in content:
                print("   ✓ دالة add_cash_invoice_button_to_toolbar موجودة")
            else:
                print("   ✗ دالة add_cash_invoice_button_to_toolbar غير موجودة")
                
            if 'create_new_cash_invoice' in content:
                print("   ✓ دالة create_new_cash_invoice موجودة")
            else:
                print("   ✗ دالة create_new_cash_invoice غير موجودة")
    else:
        print("   ✗ sales_invoice_list.js غير موجود")
    
    if os.path.exists(form_js_path):
        print("   ✓ sales_invoice.js موجود")
        
        # قراءة محتوى الملف للتحقق من عدم وجود الزر
        with open(form_js_path, 'r', encoding='utf-8') as f:
            content = f.read()
            if 'add_custom_button' in content and 'إضافة فاتورة نقدية' in content:
                print("   ⚠️ تنبيه: الزر ما زال موجود في النموذج")
            else:
                print("   ✓ الزر غير موجود في النموذج (صحيح)")
    else:
        print("   ✗ sales_invoice.js غير موجود")
    
    # 3. التحقق من hooks
    print("\n3. التحقق من hooks...")
    hooks = frappe.get_hooks()
    app_js = hooks.get('app_include_js', [])
    erp_optimizer_js = [f for f in app_js if 'erp_optimizer_ui' in f and 'sales_invoice_list' in f]
    
    if erp_optimizer_js:
        print(f"   ✓ sales_invoice_list.js مسجل في hooks")
        for js_file in erp_optimizer_js:
            print(f"     - {js_file}")
    else:
        print("   ✗ sales_invoice_list.js غير مسجل في hooks")
    
    # 4. التحقق من وجود CSS
    print("\n4. التحقق من وجود CSS...")
    css_path = '/home/<USER>/frappe-bench/apps/erp_optimizer_ui/erp_optimizer_ui/public/css/custom_theme.css'
    if os.path.exists(css_path):
        print("   ✓ custom_theme.css موجود")
    else:
        print("   ✗ custom_theme.css غير موجود")
    
    print("\n=== ملخص النتائج ===")
    print("✓ الزر يجب أن يظهر في قائمة فواتير المبيعات فقط")
    print("✓ الزر لا يظهر في نموذج الفاتورة")
    print("✓ يمكن إنشاء فاتورة نقدية من قائمة العرض")
    print("✓ الفواتير النقدية تظهر بمؤشر مختلف")
    
    print("\n=== تعليمات الاختبار ===")
    print("1. اذهب إلى: /app/sales-invoice")
    print("2. ابحث عن زر '💰 فاتورة نقدية' بجانب زر 'Add Sales Invoice'")
    print("3. انقر على الزر لإنشاء فاتورة نقدية جديدة")
    print("4. تأكد من تفعيل حقل 'فاتورة نقدية؟' في الفاتورة الجديدة")
    
    return True

if __name__ == "__main__":
    test_list_button_setup()