#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import frappe

def check_workspaces():
    """فحص مساحات العمل الموجودة"""
    
    try:
        # عرض جميع مساحات العمل
        workspaces = frappe.get_all('Workspace', fields=['name', 'label', 'module', 'parent_page'])
        
        print("=== جميع مساحات العمل الموجودة ===")
        for ws in workspaces:
            parent = ws.parent_page or "None"
            print(f"- {ws.name} | {ws.label} | Module: {ws.module} | Parent: {parent}")
        
        print(f"\nإجمالي مساحات العمل: {len(workspaces)}")
        
        # التحقق من مساحات العمل المحددة
        target_workspaces = ['Cash Invoice', 'Sales Return', 'Selling']
        
        print("\n=== التحقق من مساحات العمل المطلوبة ===")
        for ws_name in target_workspaces:
            if frappe.db.exists('Workspace', ws_name):
                ws_doc = frappe.get_doc('Workspace', ws_name)
                print(f"✅ {ws_name} موجودة - Label: {ws_doc.label} - Parent: {ws_doc.parent_page or 'None'}")
            else:
                print(f"❌ {ws_name} غير موجودة")
        
    except Exception as e:
        print(f"❌ خطأ في فحص مساحات العمل: {str(e)}")

def create_simple_workspaces():
    """إنشاء مساحات العمل بطريقة مبسطة جداً"""
    
    try:
        print("🔄 إنشاء مساحات العمل بطريقة مبسطة...")
        
        # إنشاء مساحة عمل الفاتورة النقدية
        if not frappe.db.exists('Workspace', 'Cash Invoice'):
            cash_ws = frappe.new_doc('Workspace')
            cash_ws.name = 'Cash Invoice'
            cash_ws.label = 'فاتورة نقدية'
            cash_ws.title = 'فاتورة نقدية'
            cash_ws.module = 'Selling'
            cash_ws.parent_page = 'Selling'
            cash_ws.public = 1
            cash_ws.content = '[]'
            cash_ws.insert(ignore_permissions=True)
            print("✅ تم إنشاء مساحة عمل Cash Invoice")
        else:
            print("⚠️ مساحة عمل Cash Invoice موجودة مسبقاً")
        
        # إنشاء مساحة عمل مرتجع المبيعات
        if not frappe.db.exists('Workspace', 'Sales Return'):
            return_ws = frappe.new_doc('Workspace')
            return_ws.name = 'Sales Return'
            return_ws.label = 'مرتجع مبيعات'
            return_ws.title = 'مرتجع مبيعات'
            return_ws.module = 'Selling'
            return_ws.parent_page = 'Selling'
            return_ws.public = 1
            return_ws.content = '[]'
            return_ws.insert(ignore_permissions=True)
            print("✅ تم إنشاء مساحة عمل Sales Return")
        else:
            print("⚠️ مساحة عمل Sales Return موجودة مسبقاً")
        
        frappe.db.commit()
        print("✅ تم حفظ التغييرات")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء مساحات العمل: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_workspaces()
    create_simple_workspaces()
