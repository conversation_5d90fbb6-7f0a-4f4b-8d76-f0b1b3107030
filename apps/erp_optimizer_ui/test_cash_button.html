<!DOCTYPE html>
<html>
<head>
    <title>اختبار زر الفاتورة النقدية</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .btn { padding: 8px 16px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-sm { padding: 6px 12px; font-size: 14px; }
        .page-actions { margin: 20px 0; }
        .console-output { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>اختبار زر الفاتورة النقدية</h1>
    
    <div class="page-actions">
        <button class="btn btn-primary">Add Sales Invoice</button>
    </div>
    
    <div class="console-output" id="console-output">
        <strong>Console Output:</strong><br>
    </div>
    
    <button onclick="testCashButton()">اختبار إضافة الزر</button>
    <button onclick="clearConsole()">مسح الكونسول</button>
    
    <script>
        // إعادة توجيه console.log إلى الصفحة
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const output = document.getElementById('console-output');
            output.innerHTML += args.join(' ') + '<br>';
        };
        
        function clearConsole() {
            document.getElementById('console-output').innerHTML = '<strong>Console Output:</strong><br>';
        }
        
        // نسخة مبسطة من دالة إضافة الزر
        function add_cash_invoice_button_to_page() {
            console.log('بدء البحث عن زر Add Sales Invoice...');
            
            // التحقق من عدم وجود الزر مسبقاً
            if ($('.btn-cash-invoice').length > 0) {
                console.log('زر الفاتورة النقدية موجود بالفعل');
                return;
            }

            // البحث عن زر Add Sales Invoice
            let add_btn = null;
            let selectors = [
                '.page-actions .btn-primary',
                '.btn-primary:contains("Add Sales Invoice")',
                '.btn-primary:contains("Add")',
                '.btn-primary'
            ];

            // جرب كل selector
            for (let selector of selectors) {
                add_btn = $(selector).first();
                if (add_btn.length > 0) {
                    console.log('تم العثور على الزر باستخدام:', selector, 'النص:', add_btn.text().trim());
                    break;
                }
            }

            if (add_btn.length) {
                console.log('إضافة زر الفاتورة النقدية بجانب:', add_btn.text().trim());

                // إنشاء زر الفاتورة النقدية
                let cash_invoice_btn = $(`
                    <button class="btn btn-success btn-cash-invoice btn-sm"
                            style="margin-left: 8px;"
                            title="إنشاء فاتورة نقدية جديدة">
                        💰 فاتورة نقدية
                    </button>
                `);

                // إضافة حدث النقر
                cash_invoice_btn.on('click', function (e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('تم النقر على زر الفاتورة النقدية');
                    alert('سيتم إنشاء فاتورة نقدية جديدة!');
                });

                // إدراج الزر بجانب زر Add Sales Invoice
                add_btn.after(cash_invoice_btn);

                console.log('تم إضافة زر الفاتورة النقدية بنجاح');
            } else {
                console.log('لم يتم العثور على زر Add. الأزرار المتاحة:', $('.btn-primary').map(function() { 
                    return $(this).text().trim(); 
                }).get());
            }
        }
        
        function testCashButton() {
            clearConsole();
            add_cash_invoice_button_to_page();
        }
        
        // تشغيل الاختبار تلقائياً بعد تحميل الصفحة
        $(document).ready(function() {
            console.log('تم تحميل الصفحة، بدء الاختبار...');
            setTimeout(testCashButton, 1000);
        });
    </script>
</body>
</html>
