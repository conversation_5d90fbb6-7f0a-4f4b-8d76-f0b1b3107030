// إضافة أزرار التنقل في شريط الأدوات العلوي
frappe.provide("custom_erp.navigation");

// متغيرات لتتبع تاريخ التنقل
custom_erp.navigation = {
    history: [],
    current_index: -1,
    navigating: false, // لمنع إضافة التنقل عبر الأزرار للتاريخ

    // إضافة صفحة جديدة للتاريخ
    add_to_history: function (route) {
        // إزالة الصفحات التي تأتي بعد الموضع الحالي (في حالة الرجوع ثم الذهاب لصفحة جديدة)
        this.history = this.history.slice(0, this.current_index + 1);

        // إضافة الصفحة الجديدة
        this.history.push(route);
        this.current_index = this.history.length - 1;

        // الحفاظ على حد أقصى 50 صفحة في التاريخ
        if (this.history.length > 50) {
            this.history.shift();
            this.current_index--;
        }

        this.update_buttons();
    },

    // الرجوع للصفحة السابقة
    go_back: function () {
        if (this.current_index > 0) {
            this.current_index--;
            const route = this.history[this.current_index];
            this.navigating = true; // منع إضافة هذا التنقل للتاريخ
            frappe.set_route(route);
            setTimeout(() => {
                this.navigating = false;
                this.update_buttons();
            }, 100);
        }
    },

    // الذهاب للصفحة التالية
    go_forward: function () {
        if (this.current_index < this.history.length - 1) {
            this.current_index++;
            const route = this.history[this.current_index];
            this.navigating = true; // منع إضافة هذا التنقل للتاريخ
            frappe.set_route(route);
            setTimeout(() => {
                this.navigating = false;
                this.update_buttons();
            }, 100);
        }
    },

    // تحديث حالة الأزرار
    update_buttons: function () {
        const back_btn = $('.custom-nav-back');
        const forward_btn = $('.custom-nav-forward');

        // تحديث زر الرجوع
        if (this.current_index > 0) {
            back_btn.removeClass('disabled').prop('disabled', false);
        } else {
            back_btn.addClass('disabled').prop('disabled', true);
        }

        // تحديث زر التالي
        if (this.current_index < this.history.length - 1) {
            forward_btn.removeClass('disabled').prop('disabled', false);
        } else {
            forward_btn.addClass('disabled').prop('disabled', true);
        }
    }
};

// إضافة أزرار التنقل عند تحميل الصفحة
$(document).ready(function () {
    console.log('🚀 بدء تحميل أزرار التنقل...');

    // انتظار تحميل شريط الأدوات
    setTimeout(function () {
        add_navigation_buttons();
    }, 1000);

    // محاولة إضافة الأزرار كل ثانيتين إذا لم تظهر
    const check_buttons = setInterval(function () {
        if ($('.custom-navigation-buttons').length === 0) {
            console.log('🔄 إعادة محاولة إضافة أزرار التنقل...');
            add_navigation_buttons();
        } else {
            clearInterval(check_buttons);
        }
    }, 2000);

    // إضافة الصفحة الحالية للتاريخ عند التحميل الأول
    const initial_route = frappe.get_route_str();
    if (initial_route) {
        custom_erp.navigation.add_to_history(initial_route);
    }
});

// مراقبة تغيير الصفحات باستخدام نظام Frappe
frappe.router.on('change', function () {
    setTimeout(function () {
        const current_route = frappe.get_route_str();

        // تجاهل التنقل إذا كان عبر أزرارنا
        if (!custom_erp.navigation.navigating &&
            current_route &&
            current_route !== custom_erp.navigation.history[custom_erp.navigation.current_index]) {
            custom_erp.navigation.add_to_history(current_route);
        }

        // إعادة إضافة الأزرار إذا لم تكن موجودة
        if ($('.custom-navigation-buttons').length === 0) {
            add_navigation_buttons();
        }
    }, 500);
});

function add_navigation_buttons() {
    console.log('🔍 البحث عن مكان لإضافة أزرار التنقل...');

    // البحث عن شريط البحث في أماكن مختلفة
    let search_container = $('.form-inline[role="search"]');
    console.log('📍 شريط البحث:', search_container.length);

    // إذا لم نجد شريط البحث، نبحث في أماكن أخرى
    if (search_container.length === 0) {
        search_container = $('.navbar .justify-content-end');
        console.log('📍 navbar justify-content-end:', search_container.length);
    }

    // إذا لم نجد أي مكان مناسب، نبحث في الـ navbar
    if (search_container.length === 0) {
        search_container = $('.navbar .container');
        console.log('📍 navbar container:', search_container.length);
    }

    // البحث في header
    if (search_container.length === 0) {
        search_container = $('header .container');
        console.log('📍 header container:', search_container.length);
    }

    // البحث في أي navbar موجود
    if (search_container.length === 0) {
        search_container = $('.navbar');
        console.log('📍 أي navbar:', search_container.length);
    }

    if (search_container.length === 0) {
        console.log('❌ لم يتم العثور على مكان مناسب لإضافة الأزرار');
        return;
    }

    // إزالة الأزرار إذا كانت موجودة مسبقاً
    $('.custom-navigation-buttons').remove();

    // إنشاء حاوية الأزرار
    const nav_buttons = $(`
        <div class="custom-navigation-buttons">
            <button class="btn btn-sm btn-outline-secondary custom-nav-back"
                    title="رجوع للصفحة السابقة"
                    type="button">
                <i class="fa fa-arrow-right"></i>
            </button>
            <button class="btn btn-sm btn-outline-secondary custom-nav-forward"
                    title="الذهاب للصفحة التالية"
                    type="button">
                <i class="fa fa-arrow-left"></i>
            </button>
        </div>
    `);

    // إضافة الأزرار في المكان المناسب
    if (search_container.hasClass('form-inline')) {
        search_container.prepend(nav_buttons);
    } else {
        search_container.append(nav_buttons);
    }

    // ربط الأحداث
    $('.custom-nav-back').off('click').on('click', function (e) {
        e.preventDefault();
        e.stopPropagation();
        custom_erp.navigation.go_back();
    });

    $('.custom-nav-forward').off('click').on('click', function (e) {
        e.preventDefault();
        e.stopPropagation();
        custom_erp.navigation.go_forward();
    });

    // تحديث حالة الأزرار
    custom_erp.navigation.update_buttons();

    console.log('✅ تم إضافة أزرار التنقل بنجاح');
}

frappe.ui.form.on('*', {
    refresh(frm) {
        // إظهار الزر دائمًا
        frm.add_custom_button(__('جديد'), function () {
            frappe.set_route("Form", frm.doctype, null);  // فتح نموذج جديد من نفس النوع
        });

        // تعديل شكل الزر (لون أزرق وخط أبيض)
        setTimeout(() => {
            const buttons = document.querySelectorAll('button:contains("جديد")');
            buttons.forEach(btn => {
                btn.style.backgroundColor = '#007bff';
                btn.style.color = 'white';
                btn.style.border = 'none';
                btn.style.padding = '6px 12px';
                btn.style.borderRadius = '4px';
            });
        }, 100);
    }
});
frappe.listview_settings['Purchase Invoice'] = {
    onload: function (listview) {
        const pos_profile = "نقد";
        const supplier_name = "مورد نقدي";

        // إنشاء فاتورة مشتريات نقدية
        async function create_cash_purchase_invoice() {
            try {
                const profile_doc = await frappe.db.get_doc("POS Profile", pos_profile);
                const cash_account = profile_doc.payments.find(p => p.mode_of_payment === "Cash")?.account;

                const new_invoice = frappe.model.get_new_doc('Purchase Invoice');
                new_invoice.is_paid = 1;
                new_invoice.pos_profile = pos_profile;
                new_invoice.supplier = supplier_name;
                new_invoice.mode_of_payment = "Cash"

                new_invoice.payments = [{
                    mode_of_payment: "Cash",
                    account: cash_account,
                    amount: 0
                }];

                frappe.set_route('Form', 'Purchase Invoice', new_invoice.name);
            } catch (e) {
                console.error(e);
                frappe.msgprint("حدث خطأ أثناء إنشاء الفاتورة.");
            }
        }

        // إنشاء فاتورة مرتجع يدويًا
        async function create_purchase_return_from_invoice(docname) {
            try {
                const source = await frappe.db.get_doc("Purchase Invoice", docname);

                if (source.is_return) {
                    frappe.msgprint("❌ لا يمكن إنشاء مرتجع لفاتورة هي أصلاً مرتجع.");
                    return;
                }

                // الفواتير السابقة المرتجعة
                const returned_invoices = await frappe.db.get_list("Purchase Invoice", {
                    filters: {
                        docstatus: 1,
                        is_return: 1,
                        return_against: docname
                    },
                    fields: ["name"]
                });

                let returned_items = {};
                for (let inv of returned_invoices) {
                    let return_doc = await frappe.db.get_doc("Purchase Invoice", inv.name);
                    for (let item of return_doc.items) {
                        if (!returned_items[item.item_code]) {
                            returned_items[item.item_code] = 0;
                        }
                        returned_items[item.item_code] += Math.abs(item.qty);
                    }
                }

                let new_doc = frappe.model.get_new_doc('Purchase Invoice');
                new_doc.is_return = 1;
                new_doc.return_against = source.name;
                new_doc.supplier = source.supplier;
                new_doc.posting_date = frappe.datetime.nowdate();
                new_doc.posting_time = frappe.datetime.now_time();
                new_doc.items = [];

                for (let item of source.items) {
                    const total_qty = item.qty;
                    const returned_qty = returned_items[item.item_code] || 0;
                    const remaining_qty = total_qty - returned_qty;

                    if (remaining_qty > 0) {
                        new_doc.items.push({
                            item_code: item.item_code,
                            item_name: item.item_name,
                            qty: -remaining_qty,
                            rate: item.rate,
                            uom: item.uom,
                            conversion_factor: item.conversion_factor,
                            warehouse: item.warehouse
                        });
                    }
                }

                if (new_doc.items.length === 0) {
                    frappe.msgprint("⚠️ كل أصناف هذه الفاتورة تم إرجاعها مسبقًا.");
                    return;
                }

                frappe.model.sync(new_doc);
                frappe.set_route("Form", "Purchase Invoice", new_doc.name);

            } catch (e) {
                console.error(e);
                frappe.msgprint("حدث خطأ أثناء إنشاء فاتورة المرتجع.");
            }
        }

        // زر مرتجع مشتريات
        listview.page.add_inner_button(__('🔄 مرتجع مشتريات'), async function () {
            const selected = listview.get_checked_items();

            if (selected.length === 0) {
                const new_doc = frappe.model.get_new_doc('Purchase Invoice');
                new_doc.is_return = 1;
                frappe.set_route('Form', 'Purchase Invoice', new_doc.name);
                return;
            }

            if (selected.length > 1) {
                frappe.msgprint(__('يرجى تحديد فاتورة واحدة فقط لإنشاء مرتجع.'));
                return;
            }

            const docname = selected[0].name;
            await create_purchase_return_from_invoice(docname);
        });

        // زر إنشاء فاتورة مشتريات نقدية
        listview.page.add_inner_button(__('📥 إضافة فاتورة مشتريات نقدية'), function () {
            create_cash_purchase_invoice();
        });
    }
};
// frappe.ui.form.on('Purchase Invoice', {
//     onload: function(frm) {
//         if (frm.doc.is_return) {
//             frm.set_df_property('payments_section', 'hidden', 1);
//             frm.set_df_property('base_paid_amount', 'hidden', 1);
//             frm.set_df_property('paid_amount', 'hidden', 1);
//             frm.set_df_property('change_amount', 'hidden', 1);
//             frm.set_df_property('pos_profile', 'hidden', 1);
//             frm.set_df_property('mode_of_payment', 'hidden', 1);
//             frm.set_df_property('total_advance', 'hidden', 1);
//             frm.set_df_property('write_off_amount', 'hidden', 1);
//             frm.set_df_property('return_against', 'hidden', 0);
//             frm.set_df_property('apply_tdst', 'hidden', 0);

//         } else {
//             frm.set_df_property('payments_section', 'hidden', 0);
//             frm.set_df_property('base_paid_amount', 'hidden', 0);
//             frm.set_df_property('paid_amount', 'hidden', 0);
//             frm.set_df_property('change_amount', 'hidden', 0);
//             frm.set_df_property('pos_profile', 'hidden', 0);
//             frm.set_df_property('mode_of_payment', 'hidden', 0);
//             frm.set_df_property('total_advance', 'hidden', 0);
//             frm.set_df_property('write_off_amount', 'hidden', 0);
//             frm.set_df_property('is_return', 'hidden', true);
//             frm.set_df_property('update_billed_amount_in_purchase_order', 'hidden', true);
//             frm.set_df_property('is_debit_note', 'hidden', true);
//             frm.set_df_property('apply_tdst', 'hidden', 0);
//         }
//     }
// });
frappe.ui.form.on('Purchase Invoice', {
    onload: function (frm) {
        // عرض/إخفاء الحقول حسب نوع الفاتورة
        if (frm.doc.is_return) {
            frm.set_df_property('payments_section', 'hidden', 1);
            frm.set_df_property('base_paid_amount', 'hidden', 1);
            frm.set_df_property('paid_amount', 'hidden', 1);
            frm.set_df_property('change_amount', 'hidden', 1);
            frm.set_df_property('pos_profile', 'hidden', 1);
            frm.set_df_property('mode_of_payment', 'hidden', 1);
            frm.set_df_property('total_advance', 'hidden', 1);
            frm.set_df_property('write_off_amount', 'hidden', 1);
            frm.set_df_property('return_against', 'hidden', 0);
        } else {
            frm.set_df_property('payments_section', 'hidden', 0);
            frm.set_df_property('base_paid_amount', 'hidden', 0);
            frm.set_df_property('paid_amount', 'hidden', 0);
            frm.set_df_property('change_amount', 'hidden', 0);
            frm.set_df_property('pos_profile', 'hidden', 0);
            frm.set_df_property('mode_of_payment', 'hidden', 0);
            frm.set_df_property('total_advance', 'hidden', 0);
            frm.set_df_property('write_off_amount', 'hidden', 0);
            frm.set_df_property('is_return', 'hidden', true);
            frm.set_df_property('update_billed_amount_in_purchase_order', 'hidden', true);
            frm.set_df_property('is_debit_note', 'hidden', true);
        }

        // ✅ زر جديد (آجل)
        frm.add_custom_button('🧾 جديد (آجل)', () => {
            frappe.new_doc('Purchase Invoice');
        });

        // 💵 زر جديد (نقد)
        frm.add_custom_button('💵 جديد (نقد)', async () => {
            const pos_profile = "نقد"; // عدله حسب نظامك
            const supplier_name = "مورد نقدي"; // عدله حسب المورد النقدي

            try {
                const profile_doc = await frappe.db.get_doc("POS Profile", pos_profile);
                const cash_account = profile_doc.payments.find(p => p.mode_of_payment === "Cash")?.account;

                const new_invoice = frappe.model.get_new_doc('Purchase Invoice');
                new_invoice.is_pos = 1;
                new_invoice.is_paid = 1; // نقد
                new_invoice.pos_profile = pos_profile;
                new_invoice.supplier = supplier_name;
                new_invoice.mode_of_payment = "Cash"


                // new_invoice.payments = [{
                //     mode_of_payment: "Cash",
                //     account: cash_account,
                //     amount: 0
                // }];

                frappe.set_route('Form', 'Purchase Invoice', new_invoice.name);
            } catch (e) {
                console.error(e);
                frappe.msgprint("حدث خطأ أثناء إنشاء الفاتورة النقدية.");
            }
        });
    }
});
