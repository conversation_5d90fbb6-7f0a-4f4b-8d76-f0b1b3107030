frappe.ui.form.on('*', {
    refresh(frm) {
        // إظهار الزر دائمًا
        frm.add_custom_button(__('جديد'), function () {
            frappe.set_route("Form", frm.doctype, null);  // فتح نموذج جديد من نفس النوع
        });

        // تعديل شكل الزر (لون أزرق وخط أبيض)
        setTimeout(() => {
            const buttons = document.querySelectorAll('button:contains("جديد")');
            buttons.forEach(btn => {
                btn.style.backgroundColor = '#007bff';
                btn.style.color = 'white';
                btn.style.border = 'none';
                btn.style.padding = '6px 12px';
                btn.style.borderRadius = '4px';
            });
        }, 100);
    }
});
frappe.listview_settings['Purchase Invoice'] = {
    onload: function (listview) {
        const pos_profile = "نقد";
        const supplier_name = "مورد نقدي";

        // إنشاء فاتورة مشتريات نقدية
        async function create_cash_purchase_invoice() {
            try {
                const profile_doc = await frappe.db.get_doc("POS Profile", pos_profile);
                const cash_account = profile_doc.payments.find(p => p.mode_of_payment === "Cash")?.account;

                const new_invoice = frappe.model.get_new_doc('Purchase Invoice');
                new_invoice.is_paid = 1;
                new_invoice.pos_profile = pos_profile;
                new_invoice.supplier = supplier_name;
                new_invoice.mode_of_payment = "Cash"

                new_invoice.payments = [{
                    mode_of_payment: "Cash",
                    account: cash_account,
                    amount: 0
                }];

                frappe.set_route('Form', 'Purchase Invoice', new_invoice.name);
            } catch (e) {
                console.error(e);
                frappe.msgprint("حدث خطأ أثناء إنشاء الفاتورة.");
            }
        }

        // إنشاء فاتورة مرتجع يدويًا
        async function create_purchase_return_from_invoice(docname) {
            try {
                const source = await frappe.db.get_doc("Purchase Invoice", docname);

                if (source.is_return) {
                    frappe.msgprint("❌ لا يمكن إنشاء مرتجع لفاتورة هي أصلاً مرتجع.");
                    return;
                }

                // الفواتير السابقة المرتجعة
                const returned_invoices = await frappe.db.get_list("Purchase Invoice", {
                    filters: {
                        docstatus: 1,
                        is_return: 1,
                        return_against: docname
                    },
                    fields: ["name"]
                });

                let returned_items = {};
                for (let inv of returned_invoices) {
                    let return_doc = await frappe.db.get_doc("Purchase Invoice", inv.name);
                    for (let item of return_doc.items) {
                        if (!returned_items[item.item_code]) {
                            returned_items[item.item_code] = 0;
                        }
                        returned_items[item.item_code] += Math.abs(item.qty);
                    }
                }

                let new_doc = frappe.model.get_new_doc('Purchase Invoice');
                new_doc.is_return = 1;
                new_doc.return_against = source.name;
                new_doc.supplier = source.supplier;
                new_doc.posting_date = frappe.datetime.nowdate();
                new_doc.posting_time = frappe.datetime.now_time();
                new_doc.items = [];

                for (let item of source.items) {
                    const total_qty = item.qty;
                    const returned_qty = returned_items[item.item_code] || 0;
                    const remaining_qty = total_qty - returned_qty;

                    if (remaining_qty > 0) {
                        new_doc.items.push({
                            item_code: item.item_code,
                            item_name: item.item_name,
                            qty: -remaining_qty,
                            rate: item.rate,
                            uom: item.uom,
                            conversion_factor: item.conversion_factor,
                            warehouse: item.warehouse
                        });
                    }
                }

                if (new_doc.items.length === 0) {
                    frappe.msgprint("⚠️ كل أصناف هذه الفاتورة تم إرجاعها مسبقًا.");
                    return;
                }

                frappe.model.sync(new_doc);
                frappe.set_route("Form", "Purchase Invoice", new_doc.name);

            } catch (e) {
                console.error(e);
                frappe.msgprint("حدث خطأ أثناء إنشاء فاتورة المرتجع.");
            }
        }

        // زر مرتجع مشتريات
        listview.page.add_inner_button(__('🔄 مرتجع مشتريات'), async function () {
            const selected = listview.get_checked_items();

            if (selected.length === 0) {
                const new_doc = frappe.model.get_new_doc('Purchase Invoice');
                new_doc.is_return = 1;
                frappe.set_route('Form', 'Purchase Invoice', new_doc.name);
                return;
            }

            if (selected.length > 1) {
                frappe.msgprint(__('يرجى تحديد فاتورة واحدة فقط لإنشاء مرتجع.'));
                return;
            }

            const docname = selected[0].name;
            await create_purchase_return_from_invoice(docname);
        });

        // زر إنشاء فاتورة مشتريات نقدية
        listview.page.add_inner_button(__('📥 إضافة فاتورة مشتريات نقدية'), function () {
            create_cash_purchase_invoice();
        });
    }
};
// frappe.ui.form.on('Purchase Invoice', {
//     onload: function(frm) {
//         if (frm.doc.is_return) {
//             frm.set_df_property('payments_section', 'hidden', 1);
//             frm.set_df_property('base_paid_amount', 'hidden', 1);
//             frm.set_df_property('paid_amount', 'hidden', 1);
//             frm.set_df_property('change_amount', 'hidden', 1);
//             frm.set_df_property('pos_profile', 'hidden', 1);
//             frm.set_df_property('mode_of_payment', 'hidden', 1);
//             frm.set_df_property('total_advance', 'hidden', 1);
//             frm.set_df_property('write_off_amount', 'hidden', 1);
//             frm.set_df_property('return_against', 'hidden', 0);
//             frm.set_df_property('apply_tdst', 'hidden', 0);

//         } else {
//             frm.set_df_property('payments_section', 'hidden', 0);
//             frm.set_df_property('base_paid_amount', 'hidden', 0);
//             frm.set_df_property('paid_amount', 'hidden', 0);
//             frm.set_df_property('change_amount', 'hidden', 0);
//             frm.set_df_property('pos_profile', 'hidden', 0);
//             frm.set_df_property('mode_of_payment', 'hidden', 0);
//             frm.set_df_property('total_advance', 'hidden', 0);
//             frm.set_df_property('write_off_amount', 'hidden', 0);
//             frm.set_df_property('is_return', 'hidden', true);
//             frm.set_df_property('update_billed_amount_in_purchase_order', 'hidden', true);
//             frm.set_df_property('is_debit_note', 'hidden', true);
//             frm.set_df_property('apply_tdst', 'hidden', 0);
//         }
//     }
// });
frappe.ui.form.on('Purchase Invoice', {
    onload: function(frm) {
        // عرض/إخفاء الحقول حسب نوع الفاتورة
        if (frm.doc.is_return) {
            frm.set_df_property('payments_section', 'hidden', 1);
            frm.set_df_property('base_paid_amount', 'hidden', 1);
            frm.set_df_property('paid_amount', 'hidden', 1);
            frm.set_df_property('change_amount', 'hidden', 1);
            frm.set_df_property('pos_profile', 'hidden', 1);
            frm.set_df_property('mode_of_payment', 'hidden', 1);
            frm.set_df_property('total_advance', 'hidden', 1);
            frm.set_df_property('write_off_amount', 'hidden', 1);
            frm.set_df_property('return_against', 'hidden', 0);
        } else {
            frm.set_df_property('payments_section', 'hidden', 0);
            frm.set_df_property('base_paid_amount', 'hidden', 0);
            frm.set_df_property('paid_amount', 'hidden', 0);
            frm.set_df_property('change_amount', 'hidden', 0);
            frm.set_df_property('pos_profile', 'hidden', 0);
            frm.set_df_property('mode_of_payment', 'hidden', 0);
            frm.set_df_property('total_advance', 'hidden', 0);
            frm.set_df_property('write_off_amount', 'hidden', 0);
            frm.set_df_property('is_return', 'hidden', true);
            frm.set_df_property('update_billed_amount_in_purchase_order', 'hidden', true);
            frm.set_df_property('is_debit_note', 'hidden', true);
        }

        // ✅ زر جديد (آجل)
        frm.add_custom_button('🧾 جديد (آجل)', () => {
            frappe.new_doc('Purchase Invoice');
        });

        // 💵 زر جديد (نقد)
        frm.add_custom_button('💵 جديد (نقد)', async () => {
            const pos_profile = "نقد"; // عدله حسب نظامك
            const supplier_name = "مورد نقدي"; // عدله حسب المورد النقدي

            try {
                const profile_doc = await frappe.db.get_doc("POS Profile", pos_profile);
                const cash_account = profile_doc.payments.find(p => p.mode_of_payment === "Cash")?.account;

                const new_invoice = frappe.model.get_new_doc('Purchase Invoice');
                new_invoice.is_pos = 1;
                new_invoice.is_paid = 1; // نقد
                new_invoice.pos_profile = pos_profile;
                new_invoice.supplier = supplier_name;
                new_invoice.mode_of_payment = "Cash"


                // new_invoice.payments = [{
                //     mode_of_payment: "Cash",
                //     account: cash_account,
                //     amount: 0
                // }];

                frappe.set_route('Form', 'Purchase Invoice', new_invoice.name);
            } catch (e) {
                console.error(e);
                frappe.msgprint("حدث خطأ أثناء إنشاء الفاتورة النقدية.");
            }
        });
    }
});
