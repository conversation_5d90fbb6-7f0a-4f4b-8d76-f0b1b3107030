# دليل أزرار التنقل في النظام

## نظرة عامة

تم إضافة زرين للتنقل في أعلى واجهة النظام بجانب حقل البحث:
- **زر الرجوع** (←): للعودة إلى الصفحة السابقة
- **زر التالي** (→): للانتقال إلى الصفحة التالية

## الميزات

### 1. التنقل الذكي
- يحفظ النظام تاريخ الصفحات التي زرتها (حتى 50 صفحة)
- يمكنك الرجوع والتقدم بين الصفحات بسهولة
- الأزرار تصبح غير نشطة عندما لا توجد صفحات للانتقال إليها

### 2. التصميم المتجاوب
- الأزرار تتكيف مع أحجام الشاشات المختلفة
- تصميم أنيق يتماشى مع واجهة النظام
- تأثيرات بصرية عند التمرير والنقر

### 3. سهولة الاستخدام
- أيقونات واضحة ومفهومة
- نصوص مساعدة عند التمرير
- استجابة سريعة للنقرات

## كيفية الاستخدام

### الرجوع للصفحة السابقة
1. انقر على زر الرجوع (←) في أعلى الشاشة
2. سيتم نقلك إلى آخر صفحة زرتها
3. يمكنك الاستمرار في الرجوع للصفحات السابقة

### الانتقال للصفحة التالية
1. بعد الرجوع لصفحة سابقة، يمكنك النقر على زر التالي (→)
2. سيتم نقلك إلى الصفحة التي كنت فيها قبل الرجوع
3. يمكنك التقدم حتى تصل لآخر صفحة زرتها

## الملفات المضافة

### 1. ملف JavaScript الرئيسي
```
apps/custom_erp/custom_erp/public/js/global_button.js
```
- يحتوي على منطق التنقل وإدارة التاريخ
- يضيف الأزرار إلى واجهة النظام
- يراقب تغيير الصفحات

### 2. ملف التنسيق
```
apps/custom_erp/custom_erp/public/css/navigation_buttons.css
```
- يحتوي على تنسيق الأزرار
- يدعم الشاشات المختلفة
- يتضمن تأثيرات بصرية

### 3. ملف الإعدادات
```
apps/custom_erp/custom_erp/hooks.py
```
- تم تحديثه لتضمين الملفات الجديدة
- يضمن تحميل الميزة مع النظام

## التخصيص

### تغيير عدد الصفحات المحفوظة
يمكنك تعديل الحد الأقصى للصفحات المحفوظة في التاريخ:

```javascript
// في ملف global_button.js
// الحفاظ على حد أقصى 50 صفحة في التاريخ
if (this.history.length > 50) {
    // غير الرقم 50 للعدد المطلوب
}
```

### تخصيص مظهر الأزرار
يمكنك تعديل ألوان وأحجام الأزرار في ملف CSS:

```css
/* في ملف navigation_buttons.css */
.custom-nav-back,
.custom-nav-forward {
    background-color: #f8f9fa !important;
    border: 1px solid #dee2e6 !important;
    /* أضف تخصيصاتك هنا */
}
```

## استكشاف الأخطاء

### الأزرار لا تظهر
1. تأكد من تحديث النظام بعد إضافة الملفات
2. امسح ذاكرة التخزين المؤقت للمتصفح
3. تحقق من وحدة تحكم المطور للأخطاء

### الأزرار لا تعمل
1. تحقق من وجود أخطاء JavaScript في وحدة التحكم
2. تأكد من تحميل ملف global_button.js بشكل صحيح
3. أعد تحميل الصفحة

### مشاكل في التصميم
1. تحقق من تحميل ملف navigation_buttons.css
2. امسح ذاكرة التخزين المؤقت للمتصفح
3. تحقق من تضارب أنماط CSS أخرى

## الدعم الفني

إذا واجهت أي مشاكل أو تحتاج لمساعدة في التخصيص، يرجى:
1. التحقق من سجلات النظام
2. فحص وحدة تحكم المطور في المتصفح
3. التأكد من تحديث جميع الملفات بشكل صحيح

---

**ملاحظة**: هذه الميزة تعمل مع جميع صفحات النظام وتحفظ تاريخ التنقل لكل جلسة عمل منفصلة.
