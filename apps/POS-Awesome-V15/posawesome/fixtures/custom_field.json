[{"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Item Barcode", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_uom", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 1, "in_preview": 0, "in_standard_filter": 0, "insert_after": "barcode_type", "is_system_generated": 0, "is_virtual": 0, "label": "UOM", "length": 0, "mandatory_depends_on": null, "modified": "2020-10-07 02:03:39.297065", "module": null, "name": "Item Barcode-posa_uom", "no_copy": 0, "non_negative": 0, "options": "UOM", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice Item", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_notes", "fieldtype": "Small Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "item_code", "is_system_generated": 0, "is_virtual": 0, "label": "Additional Notes", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-21 15:28:50.283526", "module": null, "name": "Sales Invoice Item-posa_notes", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Order Item", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_notes", "fieldtype": "Small Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "ensure_delivery_based_on_produced_serial_no", "is_system_generated": 0, "is_virtual": 0, "label": "Additional Notes", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-21 16:17:25.085195", "module": null, "name": "Sales Order Item-posa_notes", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice Item", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_row_id", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "item_name", "is_system_generated": 0, "is_virtual": 0, "label": "Row ID", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-08 17:56:45.705775", "module": null, "name": "Sales Invoice Item-posa_row_id", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice Item", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_delivery_date", "fieldtype": "Date", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_row_id", "is_system_generated": 0, "is_virtual": 0, "label": "Delivery Date", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-21 15:40:02.062423", "module": null, "name": "Sales Invoice Item-posa_delivery_date", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Order Item", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_row_id", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "item_name", "is_system_generated": 0, "is_virtual": 0, "label": "Row ID", "length": 0, "mandatory_depends_on": null, "modified": "2021-08-21 14:16:22.909705", "module": null, "name": "Sales Order Item-posa_row_id", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_pos_awesome_settings", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "company_address", "is_system_generated": 0, "is_virtual": 0, "label": "POS Awesome Settings", "length": 0, "mandatory_depends_on": null, "modified": "2020-10-09 15:36:23.711921", "module": null, "name": "POS Profile-posa_pos_awesome_settings", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": "For POS Closing Shift Payment Reconciliation", "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_cash_mode_of_payment", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_pos_awesome_settings", "is_system_generated": 0, "is_virtual": 0, "label": "Cash Mode of Payment", "length": 0, "mandatory_depends_on": null, "modified": "2021-03-06 00:29:24.240940", "module": null, "name": "POS Profile-posa_cash_mode_of_payment", "no_copy": 0, "non_negative": 0, "options": "Mode of Payment", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_discount", "fieldtype": "Float", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "lead_name", "is_system_generated": 0, "is_virtual": 0, "label": "Discount %", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-04 21:02:31.784347", "module": null, "name": "Customer-posa_discount", "no_copy": 0, "non_negative": 1, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_allow_delete", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_cash_mode_of_payment", "is_system_generated": 0, "is_virtual": 0, "label": "Auto Delete Draft Invoice", "length": 0, "mandatory_depends_on": null, "modified": "2020-10-09 16:01:30.649938", "module": null, "name": "POS Profile-posa_allow_delete", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "<PERSON><PERSON>", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_batch_price", "fieldtype": "Float", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "manufacturing_date", "is_system_generated": 0, "is_virtual": 0, "label": "Price", "length": 0, "mandatory_depends_on": null, "modified": "2020-10-26 02:31:58.913688", "module": null, "name": "Batch-posa_batch_price", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_allow_user_to_edit_rate", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_allow_delete", "is_system_generated": 0, "is_virtual": 0, "label": "Allow user to edit Rate", "length": 0, "mandatory_depends_on": null, "modified": "2020-10-09 16:01:30.936524", "module": null, "name": "POS Profile-posa_allow_user_to_edit_rate", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_allow_user_to_edit_additional_discount", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_allow_user_to_edit_rate", "is_system_generated": 0, "is_virtual": 0, "label": "Allow user to edit Additional Discount", "length": 0, "mandatory_depends_on": "0", "modified": "2020-10-09 16:01:31.157157", "module": null, "name": "POS Profile-posa_allow_user_to_edit_additional_discount", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": "posa_allow_user_to_edit_additional_discount", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_use_percentage_discount", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_allow_user_to_edit_additional_discount", "is_system_generated": 0, "is_virtual": 0, "label": "Use Percentage Discount", "length": 0, "mandatory_depends_on": "", "modified": "2021-09-26 14:08:06.765185", "module": null, "name": "POS Profile-posa_use_percentage_discount", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": "", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_max_discount_allowed", "fieldtype": "Float", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_use_percentage_discount", "is_system_generated": 0, "is_virtual": 0, "label": "Max Discount Percentage Allowed ", "length": 0, "mandatory_depends_on": null, "modified": "2020-10-26 05:11:52.101322", "module": null, "name": "POS Profile-posa_max_discount_allowed", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "221", "depends_on": null, "description": "It is best not to use more than four numbers", "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_scale_barcode_start", "fieldtype": "Int", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_max_discount_allowed", "is_system_generated": 0, "is_virtual": 0, "label": "Scale Barcode Start With", "length": 0, "mandatory_depends_on": null, "modified": "2020-10-30 03:54:32.270370", "module": null, "name": "POS Profile-posa_scale_barcode_start", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": "Enable camera-based barcode and QR code scanning for mobile devices", "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_enable_camera_scanning", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_scale_barcode_start", "is_system_generated": 0, "is_virtual": 0, "label": "Enable Camera Scanning", "length": 0, "mandatory_depends_on": null, "modified": "2024-01-01 12:00:00.000000", "module": null, "name": "POS Profile-posa_enable_camera_scanning", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "Both", "depends_on": "posa_enable_camera_scanning", "description": "Select which types of codes to scan with camera", "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_camera_scan_type", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_enable_camera_scanning", "is_system_generated": 0, "is_virtual": 0, "label": "Camera Scan Type", "length": 0, "mandatory_depends_on": null, "modified": "2024-01-01 12:00:00.000000", "module": null, "name": "POS Profile-posa_camera_scan_type", "no_copy": 0, "non_negative": 0, "options": "QR Code\nBarcode\nBoth", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_allow_change_posting_date", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_camera_scan_type", "is_system_generated": 0, "is_virtual": 0, "label": "Allow Change Posting Date", "length": 0, "mandatory_depends_on": null, "modified": "2022-12-16 11:20:05.134781", "module": null, "name": "POS Profile-posa_allow_change_posting_date", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "1", "depends_on": null, "description": "Display customer balance in POS screen", "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fieldname": "posa_show_customer_balance", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_allow_change_posting_date", "is_system_generated": 0, "is_virtual": 0, "label": "Show Customer Balance", "mandatory_depends_on": null, "modified": "2023-03-15 00:00:00", "module": null, "name": "POS Profile-posa_show_customer_balance", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_pos_opening_shift", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 1, "insert_after": "pos_profile", "is_system_generated": 0, "is_virtual": 0, "label": "POS Shift", "length": 0, "mandatory_depends_on": null, "modified": "2020-09-27 03:15:11.844405", "module": null, "name": "Sales Invoice-posa_pos_opening_shift", "no_copy": 1, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 1, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Address", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_delivery_charges", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "is_shipping_address", "is_system_generated": 0, "is_virtual": 0, "label": "Delivery Charges", "length": 0, "mandatory_depends_on": null, "modified": "2022-07-24 17:20:00.246026", "module": null, "name": "Address-posa_delivery_charges", "no_copy": 0, "non_negative": 0, "options": "Delivery Charges", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_default_card_view", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_show_customer_balance", "is_system_generated": 0, "is_virtual": 0, "label": "Default Card View", "length": 0, "mandatory_depends_on": null, "modified": "2023-03-12 14:03:44.088542", "module": null, "name": "POS Profile-posa_default_card_view", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_is_printed", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 1, "insert_after": "posa_pos_opening_shift", "is_system_generated": 0, "is_virtual": 0, "label": "Printed", "length": 0, "mandatory_depends_on": null, "modified": "2020-11-02 02:48:23.877227", "module": null, "name": "Sales Invoice-posa_is_printed", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_default_sales_order", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_default_card_view", "is_system_generated": 0, "is_virtual": 0, "label": "Default Sales Order", "length": 0, "mandatory_depends_on": null, "modified": "2023-03-12 14:37:41.556512", "module": null, "name": "POS Profile-posa_default_sales_order", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_col_1", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_default_sales_order", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "mandatory_depends_on": null, "modified": "2020-10-30 03:24:09.367037", "module": null, "name": "POS Profile-posa_col_1", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": "", "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_allow_user_to_edit_item_discount", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_col_1", "is_system_generated": 0, "is_virtual": 0, "label": "Allow User to Edit Item Discount", "length": 0, "mandatory_depends_on": "0", "modified": "2020-10-09 16:01:31.410384", "module": null, "name": "POS Profile-posa_allow_user_to_edit_item_discount", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_display_items_in_stock", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_allow_user_to_edit_item_discount", "is_system_generated": 0, "is_virtual": 0, "label": "Hide Unavailable Items", "length": 0, "mandatory_depends_on": "", "modified": "2020-10-09 16:01:31.663626", "module": null, "name": "POS Profile-posa_display_items_in_stock", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_allow_partial_payment", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_display_items_in_stock", "is_system_generated": 0, "is_virtual": 0, "label": "Allow Partial Payment", "length": 0, "mandatory_depends_on": null, "modified": "2020-10-09 23:06:59.598463", "module": null, "name": "POS Profile-posa_allow_partial_payment", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": "posa_allow_partial_payment", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_allow_credit_sale", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_allow_partial_payment", "is_system_generated": 0, "is_virtual": 0, "label": "Allow Credit Sale", "length": 0, "mandatory_depends_on": null, "modified": "2020-10-09 23:06:59.852139", "module": null, "name": "POS Profile-posa_allow_credit_sale", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_allow_return", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_allow_credit_sale", "is_system_generated": 0, "is_virtual": 0, "label": "Allow Return", "length": 0, "mandatory_depends_on": null, "modified": "2020-10-28 01:56:22.038314", "module": null, "name": "POS Profile-posa_allow_return", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_apply_customer_discount", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_allow_return_without_invoice", "is_system_generated": 0, "is_virtual": 0, "label": "Apply Customer Discount", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-04 21:38:33.316557", "module": null, "name": "POS Profile-posa_apply_customer_discount", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "use_cashback", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_apply_customer_discount", "is_system_generated": 0, "is_virtual": 0, "label": "Use Cashback", "length": 0, "mandatory_depends_on": null, "modified": "2021-03-24 04:35:08.517136", "module": null, "name": "POS Profile-use_cashback", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "use_customer_credit", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "use_cashback", "is_system_generated": 0, "is_virtual": 0, "label": "Use Customer Credit", "length": 0, "mandatory_depends_on": null, "modified": "2021-03-24 04:51:50.333452", "module": null, "name": "POS Profile-use_customer_credit", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_hide_closing_shift", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "use_customer_credit", "is_system_generated": 0, "is_virtual": 0, "label": "Hide Close Shift", "length": 0, "mandatory_depends_on": null, "modified": "2021-04-15 01:14:57.247333", "module": null, "name": "POS Profile-posa_hide_closing_shift", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_auto_set_batch", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_hide_closing_shift", "is_system_generated": 0, "is_virtual": 0, "label": "Auto Set Batch", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-20 20:25:37.627551", "module": null, "name": "POS Profile-posa_auto_set_batch", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_display_item_code", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_auto_set_batch", "is_system_generated": 0, "is_virtual": 0, "label": "Display Item Code", "length": 0, "mandatory_depends_on": null, "modified": "2022-05-17 02:45:24.071753", "module": null, "name": "POS Profile-posa_display_item_code", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_allow_zero_rated_items", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_display_item_code", "is_system_generated": 0, "is_virtual": 0, "label": "Allow Zero Rated Items", "length": 0, "mandatory_depends_on": null, "modified": "2022-07-20 15:09:09.652861", "module": null, "name": "POS Profile-posa_allow_zero_rated_items", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "hide_expected_amount", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_allow_zero_rated_items", "is_system_generated": 0, "is_virtual": 0, "label": "Hide Expected Amount", "length": 0, "mandatory_depends_on": null, "modified": "2022-12-15 16:57:46.117639", "module": null, "name": "POS Profile-hide_expected_amount", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_column_break_112", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "hide_expected_amount", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-23 22:48:40.886230", "module": null, "name": "POS Profile-posa_column_break_112", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_allow_sales_order", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_column_break_112", "is_system_generated": 0, "is_virtual": 0, "label": "Allow Create Sales Order", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-22 03:02:27.784096", "module": null, "name": "POS Profile-posa_allow_sales_order", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "custom_allow_select_sales_order", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_allow_sales_order", "is_system_generated": 0, "is_virtual": 0, "label": "Allow Select Sales Order", "length": 0, "mandatory_depends_on": null, "modified": "2023-11-13 12:16:27.784096", "module": null, "name": "POS Profile-custom_allow_select_sales_order", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_create_only_sales_order", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "custom_allow_select_sales_order", "is_system_generated": 0, "is_virtual": 0, "label": "Create Only Sales Order", "length": 0, "mandatory_depends_on": null, "modified": "2024-01-01 00:00:00.000000", "module": null, "name": "POS Profile-posa_create_only_sales_order", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_referral_section", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "total_monthly_sales", "is_system_generated": 0, "is_virtual": 0, "label": "Referral Code", "length": 0, "mandatory_depends_on": null, "modified": "2021-07-29 23:04:22.290849", "module": null, "name": "Company-posa_referral_section", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_show_template_items", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_allow_sales_order", "is_system_generated": 0, "is_virtual": 0, "label": "Show Template Items", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-23 22:48:41.288938", "module": null, "name": "POS Profile-posa_show_template_items", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_auto_referral", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_referral_section", "is_system_generated": 0, "is_virtual": 0, "label": "Auto Create Referral For New Customers", "length": 0, "mandatory_depends_on": null, "modified": "2021-07-29 23:07:08.681215", "module": null, "name": "Company-posa_auto_referral", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "posa_show_template_items", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_hide_variants_items", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_show_template_items", "is_system_generated": 0, "is_virtual": 0, "label": "Hide Variants Items", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-24 03:24:52.591942", "module": null, "name": "POS Profile-posa_hide_variants_items", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Order", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_additional_notes_section", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "items", "is_system_generated": 0, "is_virtual": 0, "label": "Additional Notes", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-21 16:11:59.366893", "module": null, "name": "Sales Order-posa_additional_notes_section", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_column_break_22", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_auto_referral", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "mandatory_depends_on": null, "modified": "2021-07-29 23:11:04.558635", "module": null, "name": "Company-posa_column_break_22", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_fetch_coupon", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_hide_variants_items", "is_system_generated": 0, "is_virtual": 0, "label": "Auto Fetch Coupon Gifts", "length": 0, "mandatory_depends_on": null, "modified": "2021-07-29 22:58:10.372543", "module": null, "name": "POS Profile-posa_fetch_coupon", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Order", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_notes", "fieldtype": "Small Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_additional_notes_section", "is_system_generated": 0, "is_virtual": 0, "label": "Additional Notes", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-21 16:11:59.829304", "module": null, "name": "Sales Order-posa_notes", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "posa_auto_referral", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_customer_offer", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_column_break_22", "is_system_generated": 0, "is_virtual": 0, "label": "Final Customer Offer", "length": 0, "mandatory_depends_on": "posa_auto_referral", "modified": "2021-07-29 23:11:04.891539", "module": null, "name": "Company-posa_customer_offer", "no_copy": 0, "non_negative": 0, "options": "<PERSON><PERSON>", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_birthday", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "contact_html", "is_system_generated": 0, "is_virtual": 0, "label": "Birthday", "length": 0, "mandatory_depends_on": null, "modified": "2021-07-31 00:12:09.417519", "module": null, "name": "Customer-posa_birthday", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_allow_customer_purchase_order", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_fetch_coupon", "is_system_generated": 0, "is_virtual": 0, "label": "Allow Customer Purchase Order", "length": 0, "mandatory_depends_on": null, "modified": "2021-12-16 16:27:32.300240", "module": null, "name": "POS Profile-posa_allow_customer_purchase_order", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "posa_auto_referral", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_primary_offer", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_customer_offer", "is_system_generated": 0, "is_virtual": 0, "label": "Primary Customer Offer", "length": 0, "mandatory_depends_on": null, "modified": "2021-07-29 23:11:05.290809", "module": null, "name": "Company-posa_primary_offer", "no_copy": 0, "non_negative": 0, "options": "<PERSON><PERSON>", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_referral_section", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_birthday", "is_system_generated": 0, "is_virtual": 0, "label": "Referral Code", "length": 0, "mandatory_depends_on": null, "modified": "2021-07-29 23:23:04.910503", "module": null, "name": "Customer-posa_referral_section", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_allow_print_last_invoice", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_allow_customer_purchase_order", "is_system_generated": 0, "is_virtual": 0, "label": "Allow Print Last Invoice", "length": 0, "mandatory_depends_on": null, "modified": "2021-12-16 18:00:40.631156", "module": null, "name": "POS Profile-posa_allow_print_last_invoice", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "", "depends_on": "posa_auto_referral", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Company", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_referral_campaign", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_primary_offer", "is_system_generated": 0, "is_virtual": 0, "label": "Referral Campaign", "length": 0, "mandatory_depends_on": null, "modified": "2021-07-29 23:11:05.723688", "module": null, "name": "Company-posa_referral_campaign", "no_copy": 0, "non_negative": 0, "options": "Campaign", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 1, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_referral_code", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_referral_section", "is_system_generated": 0, "is_virtual": 0, "label": "Referral Code", "length": 0, "mandatory_depends_on": null, "modified": "2021-07-29 22:42:57.772021", "module": null, "name": "Customer-posa_referral_code", "no_copy": 1, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_display_additional_notes", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_allow_print_last_invoice", "is_system_generated": 0, "is_virtual": 0, "label": "Display Additional Notes", "length": 0, "mandatory_depends_on": null, "modified": "2021-12-19 16:54:32.986600", "module": null, "name": "POS Profile-posa_display_additional_notes", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 1, "allow_on_submit": 0, "bold": 1, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_referral_company", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_referral_code", "is_system_generated": 0, "is_virtual": 0, "label": "Referral Company", "length": 0, "mandatory_depends_on": null, "modified": "2021-07-29 23:24:11.207034", "module": null, "name": "Customer-posa_referral_company", "no_copy": 1, "non_negative": 0, "options": "Company", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_allow_write_off_change", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_display_additional_notes", "is_system_generated": 0, "is_virtual": 0, "label": "Allow Write Off Change", "length": 0, "mandatory_depends_on": null, "modified": "2022-02-12 04:26:04.003374", "module": null, "name": "POS Profile-posa_allow_write_off_change", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice Item", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_offers", "fieldtype": "Small Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "pricing_rules", "is_system_generated": 0, "is_virtual": 0, "label": "POS Offers", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-07 01:51:16.390447", "module": null, "name": "Sales Invoice Item-posa_offers", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_new_line", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_allow_write_off_change", "is_system_generated": 0, "is_virtual": 0, "label": "Allow add New Items on New Line", "length": 0, "mandatory_depends_on": null, "modified": "2022-05-17 01:01:52.106645", "module": null, "name": "POS Profile-posa_new_line", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice Item", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_offer_applied", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_offers", "is_system_generated": 0, "is_virtual": 0, "label": "Offer Applied", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-12 00:12:28.473489", "module": null, "name": "Sales Invoice Item-posa_offer_applied", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_input_qty", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_new_line", "is_system_generated": 0, "is_virtual": 0, "label": "Use QTY Input", "length": 0, "mandatory_depends_on": null, "modified": "2022-05-17 02:17:13.591004", "module": null, "name": "POS Profile-posa_input_qty", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice Item", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_is_offer", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 1, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_offer_applied", "is_system_generated": 0, "is_virtual": 0, "label": "<PERSON>er", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-12 00:14:20.894553", "module": null, "name": "Sales Invoice Item-posa_is_offer", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_allow_print_draft_invoices", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_input_qty", "is_system_generated": 0, "is_virtual": 0, "label": "Allow Print Draft Invoices", "length": 0, "mandatory_depends_on": null, "modified": "2022-07-22 11:51:07.782265", "module": null, "name": "POS Profile-posa_allow_print_draft_invoices", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice Item", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_is_replace", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_is_offer", "is_system_generated": 0, "is_virtual": 0, "label": "Is Offer Replace For item Row ID", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-17 18:10:36.233226", "module": null, "name": "Sales Invoice Item-posa_is_replace", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_use_delivery_charges", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_allow_print_draft_invoices", "is_system_generated": 0, "is_virtual": 0, "label": "Use Delivery Charges", "length": 0, "mandatory_depends_on": null, "modified": "2022-07-25 11:57:17.864203", "module": null, "name": "POS Profile-posa_use_delivery_charges", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_auto_set_delivery_charges", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_use_delivery_charges", "is_system_generated": 0, "is_virtual": 0, "label": "Auto Set Delivery Charges", "length": 0, "mandatory_depends_on": null, "modified": "2022-07-25 11:57:18.367414", "module": null, "name": "POS Profile-posa_auto_set_delivery_charges", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_allow_duplicate_customer_names", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_auto_set_delivery_charges", "is_system_generated": 0, "is_virtual": 0, "label": "Allow Duplicate Customer Names", "length": 0, "mandatory_depends_on": null, "modified": "2023-06-05 15:55:21.190823", "module": null, "name": "POS Profile-posa_allow_duplicate_customer_names", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "pos_awesome_payments", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_allow_duplicate_customer_names", "is_system_generated": 0, "is_virtual": 0, "label": "POS Awesome Payments", "length": 0, "mandatory_depends_on": null, "modified": "2023-06-11 23:25:42.983974", "module": null, "name": "POS Profile-pos_awesome_payments", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_use_pos_awesome_payments", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "pos_awesome_payments", "is_system_generated": 0, "is_virtual": 0, "label": "Use POS Awesome Payments", "length": 0, "mandatory_depends_on": null, "modified": "2023-06-11 23:25:43.339584", "module": null, "name": "POS Profile-posa_use_pos_awesome_payments", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "column_break_uolvm", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_use_pos_awesome_payments", "is_system_generated": 0, "is_virtual": 0, "label": null, "length": 0, "mandatory_depends_on": null, "modified": "2023-06-11 23:29:44.857287", "module": null, "name": "POS Profile-column_break_uolvm", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "1", "depends_on": "posa_use_pos_awesome_payments", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_allow_make_new_payments", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "column_break_uolvm", "is_system_generated": 0, "is_virtual": 0, "label": "Allow Make New Payments", "length": 0, "mandatory_depends_on": null, "modified": "2023-06-11 23:28:20.478033", "module": null, "name": "POS Profile-posa_allow_make_new_payments", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "1", "depends_on": "posa_use_pos_awesome_payments", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_allow_reconcile_payments", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_allow_make_new_payments", "is_system_generated": 0, "is_virtual": 0, "label": "Allow Reconcile Payments", "length": 0, "mandatory_depends_on": null, "modified": "2023-06-11 23:28:20.897560", "module": null, "name": "POS Profile-posa_allow_reconcile_payments", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "posa_use_pos_awesome_payments", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_allow_mpesa_reconcile_payments", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_allow_reconcile_payments", "is_system_generated": 0, "is_virtual": 0, "label": "Allow Mpesa Reconcile Payments", "length": 0, "mandatory_depends_on": null, "modified": "2023-06-11 23:44:22.343030", "module": null, "name": "POS Profile-posa_allow_mpesa_reconcile_payments", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": "", "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_pos_awesome_advance_settings", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_allow_mpesa_reconcile_payments", "is_system_generated": 0, "is_virtual": 0, "label": "POS Awesome Advance Settings", "length": 0, "mandatory_depends_on": null, "modified": "2020-10-11 15:13:10.899536", "module": null, "name": "POS Profile-posa_pos_awesome_advance_settings", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": "Send invoice to submit after printing", "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_allow_submissions_in_background_job", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_pos_awesome_advance_settings", "is_system_generated": 0, "is_virtual": 0, "label": "Allow Submissions in background job", "length": 0, "mandatory_depends_on": "0", "modified": "2020-10-09 16:05:54.332880", "module": null, "name": "POS Profile-posa_allow_submissions_in_background_job", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_search_serial_no", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_allow_submissions_in_background_job", "is_system_generated": 0, "is_virtual": 0, "label": "Search by Serial Number", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-20 20:47:47.966800", "module": null, "name": "POS Profile-posa_search_serial_no", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_search_batch_no", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_search_serial_no", "is_system_generated": 0, "is_virtual": 0, "label": "Search by <PERSON><PERSON>", "length": 0, "mandatory_depends_on": null, "modified": "2023-06-05 23:50:13.126933", "module": null, "name": "POS Profile-posa_search_batch_no", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": "shipping_address_name.posa_delivery_charges", "fetch_if_empty": 1, "fieldname": "posa_delivery_charges", "fieldtype": "Link", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "taxes_and_charges", "is_system_generated": 0, "is_virtual": 0, "label": "Delivery Charges", "length": 0, "mandatory_depends_on": null, "modified": "2022-07-25 11:49:02.312879", "module": null, "name": "Sales Invoice-posa_delivery_charges", "no_copy": 0, "non_negative": 0, "options": "Delivery Charges", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "1", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_tax_inclusive", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_search_batch_no", "is_system_generated": 0, "is_virtual": 0, "label": "Tax Inclusive", "length": 0, "mandatory_depends_on": null, "modified": "2021-09-06 16:33:33.398280", "module": null, "name": "POS Profile-posa_tax_inclusive", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_delivery_charges_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "hidden": 1, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_delivery_charges", "is_system_generated": 0, "is_virtual": 0, "label": "Delivery Charges Rate", "length": 0, "mandatory_depends_on": null, "modified": "2022-07-25 11:49:04.797031", "module": null, "name": "Sales Invoice-posa_delivery_charges_rate", "no_copy": 0, "non_negative": 0, "options": "currency", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "column_break_dqsba", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_tax_inclusive", "is_system_generated": 0, "is_virtual": 0, "label": null, "length": 0, "mandatory_depends_on": null, "modified": "2023-04-24 14:44:48.969896", "module": null, "name": "POS Profile-column_break_dqsba", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "1", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_local_storage", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "column_break_dqsba", "is_system_generated": 0, "is_virtual": 0, "label": "Use Browser Local Storage", "length": 0, "mandatory_depends_on": null, "modified": "2020-11-13 22:14:13.683091", "module": null, "name": "POS Profile-posa_local_storage", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": "Use Redis cache on the server to speedup initial loads of POS Awesome ", "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_use_server_cache", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_local_storage", "is_system_generated": 0, "is_virtual": 0, "label": "Use Server Cache", "length": 0, "mandatory_depends_on": null, "modified": "2023-04-24 14:44:49.219453", "module": null, "name": "POS Profile-posa_use_server_cache", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "30", "depends_on": "posa_use_server_cache", "description": "Cache the values for n minutes", "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_server_cache_duration", "fieldtype": "Int", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_use_server_cache", "is_system_generated": 0, "is_virtual": 0, "label": "Server <PERSON><PERSON>", "length": 0, "mandatory_depends_on": null, "modified": "2023-04-24 14:44:49.341660", "module": null, "name": "POS Profile-posa_server_cache_duration", "no_copy": 0, "non_negative": 1, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "column_break_anyol", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_server_cache_duration", "is_system_generated": 0, "is_virtual": 0, "label": null, "length": 0, "mandatory_depends_on": null, "modified": "2023-06-05 16:59:17.793139", "module": null, "name": "POS Profile-column_break_anyol", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": "Use Search Limit for Items", "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "pose_use_limit_search", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "column_break_anyol", "is_system_generated": 0, "is_virtual": 0, "label": "Use Limit Search", "length": 0, "mandatory_depends_on": null, "modified": "2023-06-05 16:59:18.429778", "module": null, "name": "POS Profile-pose_use_limit_search", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "500", "depends_on": "pose_use_limit_search", "description": "Search Limit for Items\nFor best performance keep this under 1500", "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_search_limit", "fieldtype": "Int", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "pose_use_limit_search", "is_system_generated": 0, "is_virtual": 0, "label": "Search Limit Number", "length": 0, "mandatory_depends_on": "pose_use_limit_search", "modified": "2023-06-05 16:59:18.717131", "module": null, "name": "POS Profile-posa_search_limit", "no_copy": 0, "non_negative": 1, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Order", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_offers", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "pricing_rules", "is_system_generated": 0, "is_virtual": 0, "label": "POS Offers Detail", "length": 0, "mandatory_depends_on": null, "modified": "2021-08-06 15:33:25.550091", "module": null, "name": "Sales Order-posa_offers", "no_copy": 0, "non_negative": 0, "options": "POS Offer Detail", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Order", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_coupons", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_offers", "is_system_generated": 0, "is_virtual": 0, "label": "POS Coupons Detail", "length": 0, "mandatory_depends_on": null, "modified": "2021-08-06 15:32:56.710167", "module": null, "name": "Sales Order-posa_coupons", "no_copy": 0, "non_negative": 0, "options": "POS Coupon Detail", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_offers", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "pricing_rules", "is_system_generated": 0, "is_virtual": 0, "label": "POS Offers Detail", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-07 01:47:11.410905", "module": null, "name": "Sales Invoice-posa_offers", "no_copy": 0, "non_negative": 0, "options": "POS Offer Detail", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_coupons", "fieldtype": "Table", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_offers", "is_system_generated": 0, "is_virtual": 0, "label": "POS Coupons Detail", "length": 0, "mandatory_depends_on": null, "modified": "2021-07-25 01:18:29.588465", "module": null, "name": "Sales Invoice-posa_coupons", "no_copy": 0, "non_negative": 0, "options": "POS Coupon Detail", "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 1, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 1, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_additional_notes_section", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "against_income_account", "is_system_generated": 0, "is_virtual": 0, "label": "Additional Notes", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-21 15:22:41.138670", "module": null, "name": "Sales Invoice-posa_additional_notes_section", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_notes", "fieldtype": "Small Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_additional_notes_section", "is_system_generated": 0, "is_virtual": 0, "label": "Additional Notes", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-21 15:23:30.034080", "module": null, "name": "Sales Invoice-posa_notes", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_column_break_111", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_notes", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-21 15:34:20.311391", "module": null, "name": "Sales Invoice-posa_column_break_111", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": null, "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "Sales Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_delivery_date", "fieldtype": "Date", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_column_break_111", "is_system_generated": 0, "is_virtual": 0, "label": "Delivery Date", "length": 0, "mandatory_depends_on": null, "modified": "2021-06-21 15:34:20.754955", "module": null, "name": "Sales Invoice-posa_delivery_date", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": null, "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_allow_return_without_invoice", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_allow_return", "is_system_generated": 0, "is_virtual": 0, "label": "Allow Return Without Invoice", "length": 0, "mandatory_depends_on": null, "modified": "2023-03-27 10:00:00.000000", "module": null, "name": "POS Profile-posa_allow_return_without_invoice", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"doctype": "Custom Field", "dt": "POS Profile", "fieldname": "posa_allow_multi_currency", "fieldtype": "Check", "insert_after": "posa_allow_sales_order", "label": "Allow Multi Currency in POS", "modified": "2024-01-01 12:00:00.000000", "name": "POS Profile-posa_allow_multi_currency", "owner": "Administrator", "creation": "2024-01-01 12:00:00.000000", "parent": null, "parentfield": null, "parenttype": null, "idx": 0, "docstatus": 0, "__islocal": 1, "__unsaved": 1, "default": "0"}, {"doctype": "Custom Field", "dt": "POS Profile", "fieldname": "posa_allow_delete_offline_invoice", "fieldtype": "Check", "insert_after": "posa_display_discount_percentage", "label": "Allow Delete Offline Invoice", "default": "0", "name": "POS Profile-posa_allow_delete_offline_invoice"}, {"doctype": "Custom Field", "dt": "POS Profile", "fieldname": "posa_allow_price_list_rate_change", "fieldtype": "Check", "insert_after": "posa_allow_delete_offline_invoice", "label": "Allow Price List Rate Change", "default": "0", "name": "POS Profile-posa_allow_price_list_rate_change"}, {"doctype": "Custom Field", "dt": "POS Profile", "fieldname": "posa_decimal_precision", "fieldtype": "Select", "insert_after": "hide_expected_amount", "label": "Decimal Precision", "options": "0\n1\n2\n3\n4\n5", "default": "2", "name": "POS Profile-posa_decimal_precision"}, {"allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "default": "0", "depends_on": null, "description": "Reload price list Rate from server when customer changes", "docstatus": 0, "doctype": "Custom Field", "dt": "POS Profile", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "posa_force_reload_items", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "posa_use_server_cache", "is_system_generated": 0, "is_virtual": 0, "label": "Force Reload Items", "length": 0, "mandatory_depends_on": null, "modified": "2023-06-05 16:59:18.717131", "module": null, "name": "POS Profile-posa_force_reload_items", "no_copy": 0, "non_negative": 0, "options": null, "permlevel": 0, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"doctype": "Custom Field", "dt": "POS Profile", "fieldname": "posa_smart_reload_mode", "fieldtype": "Check", "insert_after": "posa_force_reload_items", "label": "Smart Reload Mode", "default": "1", "name": "POS Profile-posa_smart_reload_mode"}, {"doctype": "Custom Field", "dt": "POS Profile", "fieldname": "posa_display_discount_percentage", "fieldtype": "Check", "insert_after": "posa_display_discount_amount", "label": "Display Discount %", "default": "1", "name": "POS Profile-posa_display_discount_percentage"}, {"doctype": "Custom Field", "dt": "POS Profile", "fieldname": "posa_display_discount_amount", "fieldtype": "Check", "insert_after": "posa_silent_print", "label": "Display Discount Amount", "default": "1", "name": "POS Profile-posa_display_discount_amount"}, {"doctype": "Custom Field", "dt": "POS Profile", "fieldname": "posa_silent_print", "fieldtype": "Check", "insert_after": "posa_allow_duplicate_customer_names", "label": "Enable Silent Print", "default": "0", "name": "POS Profile-posa_silent_print"}, {"doctype": "Custom Field", "dt": "POS Profile", "fieldname": "posa_language", "fieldtype": "Select", "insert_after": "posa_default_country", "label": "POS Language", "options": null, "description": "Language for POS Awesome interface", "default": null, "name": "POS Profile-posa_language"}, {"doctype": "Custom Field", "dt": "POS Profile", "fieldname": "posa_default_country", "fieldtype": "Link", "insert_after": "hide_expected_amount", "label": "Default Country", "options": "Country", "default": "Pakistan", "name": "POS Profile-posa_default_country"}]