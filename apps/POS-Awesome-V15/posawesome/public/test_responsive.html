<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS Awesome V15 - Responsive Test</title>
    <link href="https://cdn.jsdelivr.net/npm/vuetify@3.7.5/dist/vuetify.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@mdi/font@7.4.47/css/materialdesignicons.min.css" rel="stylesheet">
    <link href="./css/responsive.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .test-header {
            background: linear-gradient(135deg, #1976d2 0%, #42a5f5 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .test-section {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .test-section:last-child {
            border-bottom: none;
        }
        
        .test-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #1976d2;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }
        
        .test-card {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
        }
        
        .breakpoint-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: #1976d2;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            z-index: 1000;
        }
        
        .responsive-demo {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .demo-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 10px 0;
        }
        
        .demo-button {
            padding: 8px 16px;
            border: 1px solid #1976d2;
            background: white;
            color: #1976d2;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .demo-button:hover {
            background: #1976d2;
            color: white;
        }
        
        .demo-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            margin: 5px 0;
            font-size: 16px;
        }
        
        .demo-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        
        .demo-table th,
        .demo-table td {
            border: 1px solid #e0e0e0;
            padding: 8px 12px;
            text-align: right;
        }
        
        .demo-table th {
            background: #f5f5f5;
            font-weight: 600;
        }
        
        /* Responsive test styles */
        @media (max-width: 575.98px) {
            .breakpoint-indicator::after {
                content: " - XS (Mobile)";
            }
            
            .demo-buttons {
                flex-direction: column;
            }
            
            .demo-button {
                min-height: 44px;
            }
        }
        
        @media (min-width: 576px) and (max-width: 767.98px) {
            .breakpoint-indicator::after {
                content: " - SM (Large Mobile)";
            }
        }
        
        @media (min-width: 768px) and (max-width: 991.98px) {
            .breakpoint-indicator::after {
                content: " - MD (Tablet)";
            }
        }
        
        @media (min-width: 992px) and (max-width: 1199.98px) {
            .breakpoint-indicator::after {
                content: " - LG (Desktop)";
            }
        }
        
        @media (min-width: 1200px) {
            .breakpoint-indicator::after {
                content: " - XL (Large Desktop)";
            }
        }
    </style>
</head>
<body>
    <div class="breakpoint-indicator">
        Current Breakpoint
    </div>
    
    <div class="test-container">
        <div class="test-header">
            <h1>POS Awesome V15 - اختبار التصميم المتجاوب</h1>
            <p>اختبار شامل لجميع التحسينات المطبقة على النظام</p>
        </div>
        
        <div class="test-section">
            <div class="test-title">1. اختبار Breakpoints والـ Media Queries</div>
            <p>قم بتغيير حجم النافذة لرؤية كيفية تغير التخطيط حسب حجم الشاشة</p>
            <div class="responsive-demo">
                <div class="test-grid">
                    <div class="test-card">
                        <h4>Mobile (XS) - أقل من 576px</h4>
                        <p>تخطيط عمودي، أزرار كبيرة، نص واضح</p>
                    </div>
                    <div class="test-card">
                        <h4>Tablet (MD) - 768px إلى 991px</h4>
                        <p>تخطيط متوسط، توازن بين المساحة والوضوح</p>
                    </div>
                    <div class="test-card">
                        <h4>Desktop (LG+) - أكبر من 992px</h4>
                        <p>تخطيط أفقي، استغلال كامل للمساحة</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">2. اختبار الأزرار والعناصر التفاعلية</div>
            <div class="responsive-demo">
                <div class="demo-buttons">
                    <button class="demo-button">زر عادي</button>
                    <button class="demo-button">حفظ</button>
                    <button class="demo-button">إلغاء</button>
                    <button class="demo-button">طباعة</button>
                    <button class="demo-button">إعدادات</button>
                </div>
                <p><strong>ملاحظة:</strong> في الشاشات الصغيرة، الأزرار تصبح أكبر وأسهل للمس</p>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">3. اختبار حقول الإدخال</div>
            <div class="responsive-demo">
                <input type="text" class="demo-input" placeholder="اسم العميل">
                <input type="tel" class="demo-input" placeholder="رقم الهاتف">
                <input type="email" class="demo-input" placeholder="البريد الإلكتروني">
                <p><strong>ملاحظة:</strong> حجم الخط 16px لمنع التكبير التلقائي في iOS</p>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">4. اختبار الجداول المتجاوبة</div>
            <div class="responsive-demo">
                <div style="overflow-x: auto;">
                    <table class="demo-table">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>المجموع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>منتج تجريبي 1</td>
                                <td>2</td>
                                <td>50.00</td>
                                <td>100.00</td>
                                <td>
                                    <button class="demo-button" style="padding: 4px 8px; font-size: 0.8rem;">تعديل</button>
                                </td>
                            </tr>
                            <tr>
                                <td>منتج تجريبي 2</td>
                                <td>1</td>
                                <td>75.00</td>
                                <td>75.00</td>
                                <td>
                                    <button class="demo-button" style="padding: 4px 8px; font-size: 0.8rem;">حذف</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <p><strong>ملاحظة:</strong> الجدول قابل للتمرير أفقياً في الشاشات الصغيرة</p>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">5. نصائح الاختبار</div>
            <div class="test-grid">
                <div class="test-card">
                    <h4>اختبار الجوال</h4>
                    <ul>
                        <li>استخدم أدوات المطور (F12)</li>
                        <li>اختر "Device Toolbar"</li>
                        <li>جرب أجهزة مختلفة</li>
                        <li>اختبر الوضع العمودي والأفقي</li>
                    </ul>
                </div>
                <div class="test-card">
                    <h4>اختبار التابلت</h4>
                    <ul>
                        <li>جرب iPad و Android tablets</li>
                        <li>اختبر الدوران</li>
                        <li>تأكد من سهولة اللمس</li>
                        <li>راجع المساحات والأحجام</li>
                    </ul>
                </div>
                <div class="test-card">
                    <h4>اختبار سطح المكتب</h4>
                    <ul>
                        <li>جرب دقات مختلفة</li>
                        <li>اختبر النوافذ الصغيرة</li>
                        <li>تأكد من استغلال المساحة</li>
                        <li>راجع الـ hover effects</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // إضافة بعض التفاعل للاختبار
        document.addEventListener('DOMContentLoaded', function() {
            console.log('POS Awesome V15 Responsive Test Loaded');
            
            // إضافة معلومات الشاشة
            function updateScreenInfo() {
                const width = window.innerWidth;
                const height = window.innerHeight;
                console.log(`Screen size: ${width}x${height}`);
            }
            
            window.addEventListener('resize', updateScreenInfo);
            updateScreenInfo();
        });
    </script>
</body>
</html>
