#!/bin/bash

# سكريبت للحصول على عنوان IP الحالي
# Script to get current IP address

echo "🔍 البحث عن عنوان IP الحالي..."
echo "🔍 Finding current IP address..."

# طرق مختلفة للحصول على IP
IP1=$(hostname -I | awk '{print $1}')
IP2=$(ip route get 1 | awk '{print $7}' | head -1)
IP3=$(ifconfig | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -1)

echo ""
echo "📍 عناوين IP المتاحة:"
echo "📍 Available IP addresses:"

if [ ! -z "$IP1" ]; then
    echo "   الطريقة 1: $IP1"
    MAIN_IP=$IP1
fi

if [ ! -z "$IP2" ]; then
    echo "   الطريقة 2: $IP2"
    if [ -z "$MAIN_IP" ]; then
        MAIN_IP=$IP2
    fi
fi

if [ ! -z "$IP3" ]; then
    echo "   الطريقة 3: $IP3"
    if [ -z "$MAIN_IP" ]; then
        MAIN_IP=$IP3
    fi
fi

echo ""
if [ ! -z "$MAIN_IP" ]; then
    echo "✅ العنوان الرئيسي: $MAIN_IP"
    echo "✅ Main IP Address: $MAIN_IP"
    echo ""
    echo "🔗 رابط النظام:"
    echo "🔗 System URL:"
    echo "   http://$MAIN_IP:8000"
    echo ""
    echo "📋 انسخ هذا الرابط واستخدمه في المتصفح"
    echo "📋 Copy this URL and use it in your browser"
else
    echo "❌ لم يتم العثور على عنوان IP"
    echo "❌ No IP address found"
fi

echo ""
